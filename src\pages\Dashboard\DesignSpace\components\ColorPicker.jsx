
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';

import PropTypes from 'prop-types';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { motion, AnimatePresence } from 'framer-motion';
import { FiX } from 'react-icons/fi';
import { MdOutlineColorLens, MdOutlinePalette, MdOutlineGradient } from 'react-icons/md';

const ColorPicker = ({ elementId, open, onClose, isMobile, isMobileStyled }) => {
    const { elements, updateElement } = useDesignSpace();
    const [isOpenState, setIsOpenState] = useState(false);
    const [selectedColor, setSelectedColor] = useState('#4338ca');
    const [colorMode, setColorMode] = useState('solid'); // solid, gradient, palette
    const [customColor, setCustomColor] = useState('#4338ca');
    const [gradientColors, setGradientColors] = useState(['#4338ca', '#8b5cf6']);
    const [gradientDirection, setGradientDirection] = useState('to right');
    const dropdownRef = useRef(null);

    const isControlled = typeof open === 'boolean' && open === true;
    const isOpen = isControlled ? open : isOpenState;
    const setIsOpen = isControlled ? (v) => { if (!v && onClose) onClose(); } : setIsOpenState;

    // Color palettes
    const colorPalettes = {
        'Basic': [
            '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80', 
            '#00FFFF', '#0080FF', '#0000FF', '#8000FF', '#FF00FF', '#FF0080'
        ],
        'Warm': [
            '#FF6B6B', '#FF8E53', '#FFA726', '#FFB74D', '#FFCC02', '#FFD54F',
            '#FFE082', '#FFECB3', '#FFF3E0', '#FFAB91', '#FF8A65', '#FF7043'
        ],
        'Cool': [
            '#4FC3F7', '#29B6F6', '#03A9F4', '#039BE5', '#0288D1', '#0277BD',
            '#01579B', '#E1F5FE', '#B3E5FC', '#81D4FA', '#4FC3F7', '#29B6F6'
        ],
        'Neutral': [
            '#F5F5F5', '#EEEEEE', '#E0E0E0', '#BDBDBD', '#9E9E9E', '#757575',
            '#616161', '#424242', '#212121', '#000000', '#FFFFFF', '#FAFAFA'
        ],
        'Nature': [
            '#8BC34A', '#CDDC39', '#FFEB3B', '#FF9800', '#FF5722', '#795548',
            '#9E9E9E', '#607D8B', '#3F51B5', '#2196F3', '#00BCD4', '#009688'
        ],
        'Trendy': [
            '#FF1744', '#F50057', '#D500F9', '#651FFF', '#3D5AFE', '#2979FF',
            '#00B0FF', '#00E5FF', '#1DE9B6', '#00E676', '#76FF03', '#C6FF00'
        ]
    };

    // Gradient directions
    const gradientDirections = [
        { value: 'to right', label: 'H', icon: '→' },
        { value: 'to bottom', label: 'V', icon: '↓' },
        { value: 'to bottom right', label: 'D', icon: '↘' },
        { value: '45deg', label: '45°', icon: '↗' },
        { value: '135deg', label: '135°', icon: '↖' },
        { value: '225deg', label: '225°', icon: '↙' },
        { value: '315deg', label: '315°', icon: '↘' }
    ];

    // Find the element

    const element = useMemo(() => elements.find(el => el.id === elementId), [elements, elementId]);

    // Optimized color application function - removed element from dependencies
    const applyColor = useCallback((color) => {
        if (!element) return;

        const updateData = {};
        // Apply color based on element type
        if (element.type === 'shape') {
            updateData.backgroundColor = color;
        } else if (element.type === 'line') {
            updateData.strokeColor = color;
        } else if (element.type === 'frame') {
            updateData.borderColor = color;
        } else if (element.type === 'text') {
            updateData.color = color;
        } else {
            // For other elements, try to apply as background
            updateData.backgroundColor = color;
        }

        // إزالة خاصية الـ background إذا كانت موجودة (لحل مشكلة بقاء الـ gradient)
        updateData.background = '';

        updateElement(elementId, updateData);
        setSelectedColor(color);
        if (!isControlled) {
            setIsOpen(false);
        }
    }, [elementId, updateElement, isControlled]); // Removed element from dependencies

    const applyGradient = useCallback(() => {
        if (!element) return;

        const gradientString = `linear-gradient(${gradientDirection}, ${gradientColors.join(', ')})`;
        const updateData = { 
            background: gradientString,
            backgroundColor: gradientString // fallback for compatibility
        };
        
        updateElement(elementId, updateData);
        setIsOpen(false);
    }, [elementId, updateElement, gradientDirection, gradientColors]); // Removed element from dependencies

    const handleCustomColorChange = useCallback((color) => {
        setCustomColor(color);
        // Call applyColor directly without dependency
        if (!element) return;

        const updateData = {};
        if (element.type === 'shape') {
            updateData.backgroundColor = color;
        } else if (element.type === 'line') {
            updateData.strokeColor = color;
        } else if (element.type === 'frame') {
            updateData.borderColor = color;
        } else if (element.type === 'text') {
            updateData.color = color;
        } else {
            updateData.backgroundColor = color;
        }
        updateData.background = '';
        updateElement(elementId, updateData);
        setSelectedColor(color);
        if (!isControlled) {
            setIsOpen(false);
        }
    }, [elementId, updateElement, isControlled]); // Removed applyColor dependency


    useEffect(() => {
        if (element) {
            // Set initial color based on element type
            if (element.backgroundColor) {
                setSelectedColor(element.backgroundColor);
                setCustomColor(element.backgroundColor);
            } else if (element.strokeColor) {
                setSelectedColor(element.strokeColor);
                setCustomColor(element.strokeColor);
            } else if (element.borderColor) {
                setSelectedColor(element.borderColor);
                setCustomColor(element.borderColor);
            }
        }
    }, [element]);

    // Handle click outside to close
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);


    // Simplified color swatch rendering - removed colorMode dependency
    useEffect(() => {
        if (isOpen) {
            // Simple timeout to ensure DOM is ready
            const timer = setTimeout(() => {
                const colorButtons = dropdownRef.current?.querySelectorAll('button[data-color]');
                if (colorButtons) {
                    colorButtons.forEach(button => {
                        const color = button.getAttribute('data-color');
                        if (color) {
                            // Apply color directly without complex observers
                            button.style.backgroundColor = color;
                            button.style.background = color;
                        }
                    });
                }
            }, 50);

            return () => clearTimeout(timer);
        }
    }, [isOpen]); // Removed colorMode dependency


    // منع السحب أثناء فتح القائمة
    useEffect(() => {
        if (isOpen) {
            document.body.classList.add('color-picker-open');

            
            // إغلاق جميع القوائم في الشريط الجانبي عند فتح ColorPicker
            const closeEvent = new CustomEvent('closeSidebarTabs');
            document.dispatchEvent(closeEvent);

        } else {
            document.body.classList.remove('color-picker-open');
        }
        return () => document.body.classList.remove('color-picker-open');
    }, [isOpen]);


    // Function to render color swatch - simplified dependencies
    const renderColorSwatch = useCallback((color, size = 'w-8 h-8') => (

        <button
            key={color}
            onClick={() => applyColor(color)}
            className={`${size} rounded-lg border-2 transition-all hover:scale-110 ${
                selectedColor === color ? 'border-gray-800 scale-110' : 'border-gray-300'
            }`}
            style={{ 
                backgroundColor: color,

                background: color

            }}
            title={color}
            data-color={color}
        />

    ), [selectedColor]); // Removed applyColor dependency


    return (
        <div className={isMobileStyled ? 'relative w-full text-white flex justify-center items-start' : (isMobile ? 'relative w-full' : 'relative')} ref={dropdownRef}>
            {/* لا تعرض زر فتح ColorPicker إذا كانت القائمة controlled (أي open !== undefined) */}
            {typeof open !== 'boolean' && (
                <motion.button
                    className={`element-control-btn color-picker-btn ${isMobileStyled ? 'text-white' : ''}`}
                    title="Change Element Color"
                    onClick={() => setIsOpen(!isOpen)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    disabled={isControlled}
                >
                    <MdOutlineColorLens size={14} />
                </motion.button>
            )}

            {/* Color Picker Dropdown */}
            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8, y: -10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.8, y: -10 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className={`${isMobileStyled ? 'w-full h-full relative mt-0 bg-transparent text-white' : (isMobile ? 'w-full relative mt-0' : 'absolute top-full left-0 mt-2 w-80 bg-white')} rounded-t-2xl shadow-2xl border border-gray-700 z-[9999] overflow-hidden`}
                        style={{
                            maxWidth: 'calc(100vw - 32px)',
                            maxHeight: 'calc(100vh - 32px)',
                            overflow: 'auto',
                        }}

                        onPointerDown={e => { e.stopPropagation(); }}
                        onMouseDown={e => { e.stopPropagation(); }}
                        onTouchStart={e => { e.stopPropagation(); }}

                    >
                        {/* Header */}
                        <div className={`bg-gradient-to-r from-purple-800 to-blue-900 p-4 ${isMobileStyled ? 'text-white' : 'text-white'}`}>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center">

                                    <h3 className="font-semibold">Color Selector</h3>
                                </div>
                                <button
                                    onClick={() => setIsOpen(false)}
                                    className={isMobileStyled ? 'text-gray-200 hover:text-white transition-colors' : 'text-white/80 hover:text-white transition-colors'}
                                >
                                    <FiX size={18} />
                                </button>
                            </div>
                        </div>

                        {/* Mode Tabs */}
                        <div className={`flex border-b ${isMobileStyled ? 'border-gray-700' : 'border-gray-200'}` }>
                            {[
                                { id: 'solid', label: 'Solid', icon: <MdOutlineColorLens size={16} /> },
                                { id: 'gradient', label: 'Gradient', icon: <MdOutlineGradient size={16} /> },
                                { id: 'palette', label: 'Palettes', icon: <MdOutlinePalette size={16} /> }
                            ].map(mode => (
                                <button
                                    key={mode.id}
                                    onClick={() => setColorMode(mode.id)}
                                    className={`flex-1 flex items-center justify-center py-3 px-4 text-sm font-medium transition-colors ${
                                        colorMode === mode.id
                                            ? (isMobileStyled ? 'text-white border-b-2 border-white bg-gray-800' : 'text-purple-600 border-b-2 border-purple-600 bg-purple-50')
                                            : (isMobileStyled ? 'text-gray-200 hover:text-white hover:bg-gray-800' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50')
                                    }`}
                                >
                                    <span className="mr-1">{mode.icon}</span>
                                    {mode.label}
                                </button>
                            ))}
                        </div>

                        {/* Content */}
                        <div className={`p-4 max-h-96 overflow-y-auto ${isMobileStyled ? 'text-white' : ''}`} style={isMobileStyled ? {width: '100%', maxWidth: 420} : {}}>
                            {/* Solid Color Mode */}
                            {colorMode === 'solid' && (
                                <div className="space-y-4">
                                    {/* Custom Color Picker */}
                                    <div>
                                        <label className={`block text-sm font-medium mb-2 ${isMobileStyled ? 'text-white' : 'text-gray-700'}`}>
                                            Custom
                                        </label>
                                        <div className="flex items-center space-x-3">
                                            <input
                                                type="color"
                                                value={customColor}
                                                onChange={(e) => handleCustomColorChange(e.target.value)}
                                                className="w-12 h-12 rounded-lg border-2 border-gray-300 cursor-pointer hover:border-purple-500 transition-colors"
                                            />
                                            <input
                                                type="text"
                                                value={customColor}
                                                onChange={(e) => handleCustomColorChange(e.target.value)}
                                                className={`flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm font-mono ${isMobileStyled ? 'bg-gray-900 border-gray-700 placeholder-gray-400' : ''}`}
                                                style={{ color: '#111' }}
                                                placeholder="#RRGGBB"
                                            />
                                        </div>
                                    </div>

                                    {/* Quick Colors */}
                                    <div>
                                        <label className={`block text-sm font-medium mb-2 ${isMobileStyled ? 'text-white' : 'text-gray-700'}`}>
                                            Quick
                                        </label>
                                        <div className="grid grid-cols-8 gap-2 w-full">
                                            {[
                                                '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80', '#00FFFF', '#0080FF',
                                                '#0000FF', '#8000FF', '#FF00FF', '#FF0080', '#FF6B6B', '#4FC3F7', '#8BC34A', '#9E9E9E'
                                            ].map(color => renderColorSwatch(color))}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Gradient Mode */}
                            {colorMode === 'gradient' && (
                                <div className="space-y-4">
                                    {/* Gradient Preview */}
                                    <div className="relative">
                                        <div

                                            className="h-16 rounded-lg border border-gray-300 overflow-hidden"
                                            style={{
                                                background: `linear-gradient(${gradientDirection}, ${gradientColors.join(', ')})`

                                            }}
                                            title="Gradient Preview"
                                        >
                                        </div>
                                    </div>

                                    {/* Gradient Colors */}
                                    <div>
                                        <label className={`block text-sm font-medium mb-2 ${isMobileStyled ? 'text-white' : 'text-gray-700'}`}>
                                            Colors
                                        </label>
                                        <div className="flex items-center space-x-3">
                                            {gradientColors.map((color, index) => (
                                                <div key={index} className="flex items-center space-x-2">
                                                    <input
                                                        type="color"
                                                        value={color}
                                                        onChange={(e) => {
                                                            const newColors = [...gradientColors];
                                                            newColors[index] = e.target.value;
                                                            setGradientColors(newColors);
                                                        }}
                                                        className="w-10 h-10 rounded-lg border-2 border-gray-300 cursor-pointer"
                                                    />
                                                    {gradientColors.length > 2 && (
                                                        <button
                                                            onClick={() => {
                                                                const newColors = gradientColors.filter((_, i) => i !== index);
                                                                setGradientColors(newColors);
                                                            }}
                                                            className={isMobileStyled ? 'text-red-300 hover:text-red-500' : 'text-red-500 hover:text-red-700'}
                                                        >
                                                            <FiX size={14} />
                                                        </button>
                                                    )}
                                                </div>
                                            ))}
                                            {gradientColors.length < 5 && (
                                                <button
                                                    onClick={() => setGradientColors([...gradientColors, '#000000'])}
                                                    className={`w-10 h-10 border-2 border-dashed rounded-lg flex items-center justify-center ${isMobileStyled ? 'border-gray-700 text-gray-300 hover:border-white hover:text-white' : 'border-gray-300 text-gray-500 hover:border-purple-500 hover:text-purple-500'}`}
                                                >
                                                    <FiX size={16} className="rotate-45" />
                                                </button>
                                            )}
                                        </div>
                                    </div>

                                    {/* Gradient Direction */}
                                    <div>
                                        <label className={`block text-sm font-medium mb-2 ${isMobileStyled ? 'text-white' : 'text-gray-700'}`}>
                                            Direction
                                        </label>
                                        <div className="grid grid-cols-4 gap-2">
                                            {gradientDirections.map(direction => (
                                                <button
                                                    key={direction.value}
                                                    onClick={() => setGradientDirection(direction.value)}
                                                    className={`p-2 rounded-lg border-2 text-sm transition-all ${
                                                        gradientDirection === direction.value
                                                            ? (isMobileStyled ? 'border-white bg-gray-800 text-white' : 'border-purple-500 bg-purple-50 text-purple-700')
                                                            : (isMobileStyled ? 'border-gray-700 text-gray-200 hover:border-white hover:text-white' : 'border-gray-300 hover:border-gray-400')
                                                    }`}
                                                >
                                                    <div className="text-lg mb-1">{direction.icon}</div>
                                                    <div className="text-xs">{direction.label}</div>
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Apply Gradient Button */}

                                    <button
                                        onClick={applyGradient}
                                        className={`w-full px-4 py-2 rounded-lg font-bold text-sm bg-gradient-to-r from-red-500 to-orange-500 text-white shadow hover:scale-105 transition-all duration-300 flex items-center justify-center mt-4 ${isMobileStyled ? 'border border-white' : ''}`}
                                        style={{
                                            minHeight: '48px'
                                        }}
                                    >
                                        Apply
                                    </button>

                                </div>
                            )}

                            {/* Palette Mode */}
                            {colorMode === 'palette' && (
                                <div className="space-y-4">
                                    {Object.entries(colorPalettes).map(([paletteName, colors]) => (
                                        <div key={paletteName}>
                                            <label className={`block text-sm font-medium mb-2 ${isMobileStyled ? 'text-white' : 'text-gray-700'}`}>
                                                {paletteName}
                                            </label>
                                            <div className="grid grid-cols-6 gap-2">
                                                {colors.map(color => renderColorSwatch(color))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

ColorPicker.defaultProps = {
    open: undefined,
    onClose: undefined,
    isMobile: false,
    isMobileStyled: false
};

ColorPicker.propTypes = {
    elementId: PropTypes.string.isRequired,
    open: PropTypes.bool,
    onClose: PropTypes.func,
    isMobile: PropTypes.bool,
    isMobileStyled: PropTypes.bool
};

export default ColorPicker; 