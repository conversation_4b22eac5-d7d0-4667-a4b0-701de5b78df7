import React, { useState } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Slider } from 'primereact/slider';
import { Chip } from 'primereact/chip';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import PropTypes from 'prop-types';

const AIToolsHub = ({ onlyImageTab = false, onlyTextTab = false }) => {
    // Get design space context
    const {
        updateElement,
        selectedIds,
        elements
    } = useDesignSpace();
    
    // إعادة تعريف المتغيرات المفقودة
    const [isProcessing] = useState(false);
    const [resultMessage, setResultMessage] = useState(null);
    const [generatedImage] = useState(null);
    const [imageEditMode] = useState('style'); // تغيير إلى 'style' كقيمة افتراضية
    const [imageEditedVersion] = useState(null);

    // Get the currently selected image (if any)
    const selectedImage = React.useMemo(() => {
        if (selectedIds.length === 1) {
            const selectedElement = elements.find(el => el.id === selectedIds[0]);
            if (selectedElement && selectedElement.type === 'img') {
                return selectedElement;
            }
        }
        return null;
    }, [selectedIds, elements]);

    // Image generation and editing states
    const [imageEnhanceLevel, setImageEnhanceLevel] = useState(50);
    const [imageBackgroundPrompt, setImageBackgroundPrompt] = useState('');


    // بعد useState مباشرةً
    const [imageStyle, setImageStyle] = useState('realistic');
    const imageStyles = {
        'realistic': { filter: 'none', label: 'Realistic' },
        'watercolor': { filter: 'saturate(1.3) contrast(0.8) brightness(1.1) blur(0.5px)', label: 'Watercolor' },
        'oil-painting': { filter: 'saturate(1.5) contrast(1.2) brightness(0.9)', label: 'Oil Painting' },
        'sketch': { filter: 'grayscale(1) contrast(1.5) brightness(1.2)', label: 'Sketch' },
        'vintage': { filter: 'sepia(0.5) contrast(0.9) brightness(0.9)', label: 'Vintage' },
        'noir': { filter: 'grayscale(1) contrast(1.3) brightness(0.8)', label: 'Noir' },
        'pop-art': { filter: 'saturate(2) contrast(1.5) brightness(1.2) hue-rotate(5deg)', label: 'Pop Art' },
        'comic': { filter: 'saturate(1.7) contrast(1.8) brightness(1.1)', label: 'Comic' },
        'cyberpunk': { filter: 'saturate(1.5) contrast(1.2) brightness(1.1) hue-rotate(270deg)', label: 'Cyberpunk' },
        'fantasy': { filter: 'saturate(1.4) contrast(1.1) brightness(1.1) hue-rotate(15deg)', label: 'Fantasy' },
        'duotone': { filter: 'grayscale(1) sepia(0.8) hue-rotate(180deg)', label: 'Duotone' },
        'neon': { filter: 'brightness(1.2) contrast(1.5) saturate(2) hue-rotate(300deg)', label: 'Neon' },
        'pastel': { filter: 'brightness(1.1) contrast(0.8) saturate(0.7)', label: 'Pastel' },
        'dramatic': { filter: 'contrast(1.5) brightness(0.9) saturate(1.3)', label: 'Dramatic' },
        'cinematic': { filter: 'contrast(1.2) brightness(0.9) saturate(1.1) sepia(0.2)', label: 'Cinematic' }
    };

    function handleApplyImageStyle() {
        if (selectedImage && imageStyle !== 'realistic') {
            // تطبيق الفلتر على الصورة المحددة
            const filterValue = imageStyles[imageStyle].filter;
            
            // تحديث عنصر الصورة المحدد مع الفلتر الجديد
            updateElement(selectedImage.id, {
                style: {
                    ...selectedImage.style,
                    filter: filterValue
                }
            });
            
            // تطبيق الأنماط مباشرة على عنصر الصورة لتجنب التأثير على شريط الأدوات
            const imageElement = document.querySelector(`[data-element-id="${selectedImage.id}"] img`);
            if (imageElement) {
                imageElement.style.filter = filterValue;
            }
            
            // إظهار رسالة نجاح
            setResultMessage({
                severity: 'success',
                summary: 'Success',
                detail: `${imageStyles[imageStyle].label} style applied successfully`
            });
            
            // إعادة تعيين الرسالة بعد 3 ثوان
            setTimeout(() => {
                setResultMessage(null);
            }, 3000);
        } else if (!selectedImage) {
            // إظهار رسالة تحذير إذا لم يتم تحديد صورة
            setResultMessage({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please select an image first'
            });
            
            // إعادة تعيين الرسالة بعد 3 ثوان
            setTimeout(() => {
                setResultMessage(null);
            }, 3000);
        }
    }

    function handleResetImageStyle() {
        if (selectedImage) {
            // إزالة الفلتر من عنصر الصورة المحدد
            updateElement(selectedImage.id, {
                style: {
                    ...selectedImage.style,
                    filter: 'none'
                }
            });
            
            // إزالة الأنماط مباشرة من عنصر الصورة
            const imageElement = document.querySelector(`[data-element-id="${selectedImage.id}"] img`);
            if (imageElement) {
                imageElement.style.filter = 'none';
            }
            
            // إعادة تعيين نمط الصورة إلى realistic
            setImageStyle('realistic');
            
            // إظهار رسالة نجاح
            setResultMessage({
                severity: 'success',
                summary: 'Success',
                detail: 'Image style reset to original successfully'
            });
            
            // إعادة تعيين الرسالة بعد 3 ثوان
            setTimeout(() => {
                setResultMessage(null);
            }, 3000);
        } else {
            // إظهار رسالة تحذير إذا لم يتم تحديد صورة
            setResultMessage({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please select an image first'
            });
            
            // إعادة تعيين الرسالة بعد 3 ثوان
            setTimeout(() => {
                setResultMessage(null);
            }, 3000);
        }
    }


    function handleEnhanceImage() {}
    function handleChangeBackground() {}
    function handleAddImageToCanvas() {}


    if (onlyImageTab) {
        // Render only the Image tab content (no TabView, no other tabs)
    return (
        <div className="ai-tools-hub h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: 'white' }} onClick={(e) => e.stopPropagation()}>
                {/* عرض رسائل النجاح/التحذير */}
                {resultMessage && (
                    <div className="p-3 border-b border-gray-200">
                        <div className={`p-3 rounded ${
                            resultMessage.severity === 'success' ? 'bg-green-50 border border-green-200 text-green-800' :
                            resultMessage.severity === 'warn' ? 'bg-yellow-50 border border-yellow-200 text-yellow-800' :
                            'bg-red-50 border border-red-200 text-red-800'
                        }`}>
                            <div className="font-medium">{resultMessage.summary}</div>
                            <div className="text-sm">{resultMessage.detail}</div>
                        </div>
                    </div>
                )}
                <div style={{ flexGrow: 1, overflowY: 'auto', padding: '0', height: '100%', position: 'relative' }}>
                    {/* Render only the Image tab content */}
                    <div className="p-4">
                        {/* Selected Image Info */}
                        {selectedImage ? (
                            <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-100">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">Selected Image</span>
                                    Selected Image:
                                </h5>
                                <div className="flex items-center">
                                    <div className="w-16 h-16 mr-3 border rounded overflow-hidden">
                                        <img
                                            src={selectedImage.value}
                                            alt="Selected"
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div>
                                        <p className="text-xs text-blue-800 font-medium">ID: {selectedImage.id}</p>
                                        <p className="text-xs text-blue-600">Apply AI tools to edit this image directly on canvas</p>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="mb-4 p-3 bg-yellow-50 rounded border border-yellow-100">
                                <p className="text-sm text-yellow-700">
                                    <span className="font-medium">Tip:</span> Select an image on the canvas to edit it directly, or generate a new one below.
                                </p>
                            </div>
                        )}
                        {/* أدوات style تظهر مباشرة */}
                        <div>
                            <div className="mb-3">
                                <label className="block text-sm font-medium mb-2">Select Style</label>
                                <div className="grid grid-cols-2 gap-2">
                                    {Object.entries(imageStyles).map(([key, style]) => (
                                        <div
                                            key={key}
                                            className={`p-2 border rounded cursor-pointer text-center ${imageStyle === key ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                            onClick={() => setImageStyle(key)}
                                        >
                                            {style.label}
                                </div>
                                    ))}
                                    </div>
                                </div>
                                <div className="flex justify-between mb-4">
                                    <Button
                                        label="Reset"
                                        icon="pi pi-refresh"
                                        className="p-button-outlined p-button-sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleResetImageStyle();
                                        }}
                                        disabled={isProcessing && (!generatedImage && !selectedImage)}
                                    />
                                    <Button
                                    label={isProcessing ? "Applying..." : "Apply Style"}
                                        icon="pi pi-image"
                                        loading={isProcessing}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleApplyImageStyle();
                                    }}
                                    disabled={(!generatedImage && !selectedImage) || isProcessing}
                                        className="p-button-sm"
                                    />
                                </div>
                            </div>
                        {/* Enhance Image Tab */}
                        {imageEditMode === 'enhance' && (
                            <div>
                                <div className="mb-3">
                                    <label className="block text-sm font-medium mb-1">Enhancement Level: {imageEnhanceLevel}%</label>
                                    <Slider
                                        value={imageEnhanceLevel}
                                        onChange={(e) => setImageEnhanceLevel(e.value)}
                                        min={0}
                                        max={100}
                                        disabled={isProcessing && (!generatedImage && !selectedImage)}
                                    />
                                </div>
                                <div className="flex justify-between mb-4">
                                    <Button
                                        label="Reset"
                                        icon="pi pi-refresh"
                                        className="p-button-outlined p-button-sm"
                                        onClick={() => setImageEnhanceLevel(50)}
                                        disabled={isProcessing && (!generatedImage && !selectedImage)}
                                    />
                                    <Button
                                        label={isProcessing ? "Enhancing..." : "Enhance Image"}
                                        icon="pi pi-image"
                                        loading={isProcessing}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleEnhanceImage();
                                        }}
                                        disabled={(!generatedImage && !selectedImage) || isProcessing}
                                        className="p-button-sm"
                                    />
                                </div>
                            </div>
                        )}
                        {/* Background Image Tab */}
                        {imageEditMode === 'background' && (
                            <div>
                                <div className="mb-3">
                                    <label htmlFor="backgroundPrompt" className="block text-sm font-medium mb-1">Describe the new background</label>
                                    <InputText
                                        id="backgroundPrompt"
                                        value={imageBackgroundPrompt}
                                        onChange={(e) => setImageBackgroundPrompt(e.target.value)}
                                        placeholder="e.g., A beach sunset"
                                        className="w-full"
                                        disabled={isProcessing && (!generatedImage && !selectedImage)}
                                    />
                                    <div className="mt-2">
                                        <div className="flex flex-wrap gap-1">
                                            <Chip label="Beach" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('beach background')} />
                                            <Chip label="Space" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('space background')} />
                                            <Chip label="Forest" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('forest background')} />
                                            <Chip label="City" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('city background')} />
                                            <Chip label="Mountains" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('mountain background')} />
                                            <Chip label="Abstract" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('abstract colorful background')} />
                                            <Chip label="Gradient" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('gradient background')} />
                                            <Chip label="Office" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('modern office background')} />
                                        </div>
                                    </div>
                                </div>
                                <div className="flex justify-between mb-4">
                                    <Button
                                        label="Reset"
                                        icon="pi pi-refresh"
                                        className="p-button-outlined p-button-sm"
                                        onClick={() => setImageBackgroundPrompt('')}
                                        disabled={isProcessing && (!generatedImage && !selectedImage)}
                                    />
                                    <Button
                                        label={isProcessing ? "Changing..." : "Change Background"}
                                        icon="pi pi-image"
                                        loading={isProcessing}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleChangeBackground();
                                        }}
                                        disabled={((!generatedImage && !selectedImage) || !imageBackgroundPrompt || isProcessing)}
                                        className="p-button-sm"
                                    />
                                </div>
                            </div>
                        )}
                        {/* Image Preview - Only show if no image is selected or we're in generate mode */}
                        {(!selectedImage || imageEditMode === 'generate') && (
                            <div className="mt-4 p-3 border rounded bg-gradient-to-r from-green-50 to-teal-50 shadow-sm">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-green-500 text-white text-xs px-2 py-1 rounded mr-2">Image</span>
                                    Image Preview:
                                </h5>
                                {generatedImage ? (
                                    <>
                                        <div className="border rounded overflow-hidden bg-white shadow-sm">
                                            <img
                                                src={imageEditedVersion || generatedImage}
                                                alt="Generated"
                                                className="max-w-full h-auto mx-auto"
                                                style={{
                                                    maxHeight: '200px',
                                                    objectFit: 'contain',
                                                    filter: imageStyle !== 'realistic' ? imageStyles[imageStyle].filter : 'none',
                                                    backgroundColor: imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? 'transparent' : undefined
                                                }}
                                            />
                                        </div>
                                        <div className="mt-3 flex justify-end">
                                            <Button
                                                label="Add to Canvas"
                                                icon="pi pi-plus"
                                                className="p-button-sm p-button-outlined"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleAddImageToCanvas(imageEditedVersion || generatedImage);
                                                }}
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <p className="text-sm text-gray-500 italic p-2 bg-white rounded border border-gray-100">
                                        The image will appear here after generation
                                    </p>
                                )}
                            </div>
                        )}
                        {/* Preview of edits on selected image - Only show if an image is selected and we're not in generate mode */}
                        {selectedImage && imageEditMode !== 'generate' && (
                            <div className="mt-4 p-3 border rounded bg-gradient-to-r from-blue-50 to-indigo-50 shadow-sm">
                                <h5 className="text-sm font-medium mb-2 flex items-center">
                                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">Edit Preview</span>
                                    Edit Preview:
                                </h5>
                                <div className="flex flex-col md:flex-row gap-4">
                                    <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                        <h6 className="text-xs font-medium text-center mb-2 text-gray-500">Original</h6>
                                        <img
                                            src={selectedImage.value}
                                            alt="Original"
                                            className="max-w-full h-auto mx-auto"
                                            style={{
                                                maxHeight: '150px',
                                                objectFit: 'contain'
                                            }}
                                        />
                                    </div>
                                    <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                        <h6 className="text-xs font-medium text-center mb-2 text-gray-500">With Edits</h6>
                                        {imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? (
                                            <div className="relative">
                                                <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
                                                    {[...Array(64)].map((_, i) => (
                                                        <div
                                                            key={i}
                                                            className={`${(Math.floor(i / 8) + i % 8) % 2 === 0 ? 'bg-gray-200' : 'bg-white'}`}
                                                        ></div>
                                                    ))}
                                                </div>
                                                <img
                                                    src={selectedImage.value}
                                                    alt="With Edits"
                                                    className="max-w-full h-auto mx-auto relative"
                                                    style={{
                                                        maxHeight: '150px',
                                                        objectFit: 'contain',
                                                        filter: 'contrast(1.2) brightness(1.1)',
                                                        mixBlendMode: 'multiply'
                                                    }}
                                                />
                                            </div>
                                        ) : (
                                            <img
                                                src={selectedImage.value}
                                                alt="With Edits"
                                                className="max-w-full h-auto mx-auto"
                                                style={{
                                                    maxHeight: '150px',
                                                    objectFit: 'contain',
                                                    filter: imageStyle !== 'realistic'
                                                        ? imageStyles[imageStyle].filter
                                                        : imageEditMode === 'enhance'
                                                            ? `contrast(${1 + imageEnhanceLevel/100}) brightness(${1 + imageEnhanceLevel/200}) saturate(${1 + imageEnhanceLevel/100})`
                                                            : 'none'
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>
                                <div className="mt-3 text-xs text-center text-gray-500">
                                    Changes are applied directly to the selected image on the canvas
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    if (onlyTextTab) {
        // Render only the Text tab content (no TabView, no other tabs)
        return (
            <div className="ai-tools-hub h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: 'white' }} onClick={(e) => e.stopPropagation()}>
                <div className="p-4">
                    <p className="text-center text-gray-500">Text tools have been moved to TextSettings component</p>
                </div>
            </div>
        );
    }

    return (
        <div className="ai-tools-hub h-full" style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: 'white' }} onClick={(e) => e.stopPropagation()}>
            {/* عرض رسائل النجاح/التحذير */}
            {resultMessage && (
                <div className="p-3 border-b border-gray-200">
                    <div className={`p-3 rounded ${
                        resultMessage.severity === 'success' ? 'bg-green-50 border border-green-200 text-green-800' :
                        resultMessage.severity === 'warn' ? 'bg-yellow-50 border border-yellow-200 text-yellow-800' :
                        'bg-red-50 border border-red-200 text-red-800'
                    }`}>
                        <div className="font-medium">{resultMessage.summary}</div>
                        <div className="text-sm">{resultMessage.detail}</div>
                    </div>
                </div>
            )}
            <div style={{ flexGrow: 1, overflowY: 'auto', padding: '0', height: '100%', position: 'relative' }}>
                        <div className="p-4">
                            {/* Selected Image Info */}
                            {selectedImage ? (
                                <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-100">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">Selected Image</span>
                                        Selected Image:
                                    </h5>
                                    <div className="flex items-center">
                                        <div className="w-16 h-16 mr-3 border rounded overflow-hidden">
                                            <img
                                                src={selectedImage.value}
                                                alt="Selected"
                                                className="w-full h-full object-cover"
                                            />
                                        </div>
                                        <div>
                                            <p className="text-xs text-blue-800 font-medium">ID: {selectedImage.id}</p>
                                            <p className="text-xs text-blue-600">Apply AI tools to edit this image directly on canvas</p>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="mb-4 p-3 bg-yellow-50 rounded border border-yellow-100">
                                    <p className="text-sm text-yellow-700">
                                        <span className="font-medium">Tip:</span> Select an image on the canvas to edit it directly, or generate a new one below.
                                    </p>
                                </div>
                            )}
                    {/* أدوات style تظهر مباشرة */}
                    <div>
                        <div className="mb-3">
                            <label className="block text-sm font-medium mb-2">Select Style</label>
                            <div className="grid grid-cols-2 gap-2">
                                {Object.entries(imageStyles).map(([key, style]) => (
                                    <div
                                        key={key}
                                        className={`p-2 border rounded cursor-pointer text-center ${imageStyle === key ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                        onClick={() => setImageStyle(key)}
                                    >
                                        {style.label}
                                    </div>
                                ))}
                                    </div>
                                    </div>
                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleResetImageStyle();
                                }}
                                disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                label={isProcessing ? "Applying..." : "Apply Style"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleApplyImageStyle();
                                }}
                                disabled={(!generatedImage && !selectedImage) || isProcessing}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            {/* Enhance Image Tab */}
                            {imageEditMode === 'enhance' && (
                                <div>
                                    <div className="mb-3">
                                        <label className="block text-sm font-medium mb-1">Enhancement Level: {imageEnhanceLevel}%</label>
                                        <Slider
                                            value={imageEnhanceLevel}
                                            onChange={(e) => setImageEnhanceLevel(e.value)}
                                            min={0}
                                            max={100}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                    </div>
                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setImageEnhanceLevel(50)}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                            label={isProcessing ? "Enhancing..." : "Enhance Image"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleEnhanceImage();
                                            }}
                                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}
                            {/* Background Image Tab */}
                            {imageEditMode === 'background' && (
                                <div>
                                    <div className="mb-3">
                                        <label htmlFor="backgroundPrompt" className="block text-sm font-medium mb-1">Describe the new background</label>
                                        <InputText
                                            id="backgroundPrompt"
                                            value={imageBackgroundPrompt}
                                            onChange={(e) => setImageBackgroundPrompt(e.target.value)}
                                            placeholder="e.g., A beach sunset"
                                            className="w-full"
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <div className="mt-2">
                                            <div className="flex flex-wrap gap-1">
                                                <Chip label="Beach" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('beach background')} />
                                                <Chip label="Space" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('space background')} />
                                                <Chip label="Forest" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('forest background')} />
                                                <Chip label="City" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('city background')} />
                                                <Chip label="Mountains" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('mountain background')} />
                                                <Chip label="Abstract" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('abstract colorful background')} />
                                                <Chip label="Gradient" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('gradient background')} />
                                                <Chip label="Office" className="text-xs cursor-pointer" onClick={() => setImageBackgroundPrompt('modern office background')} />
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex justify-between mb-4">
                                        <Button
                                            label="Reset"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setImageBackgroundPrompt('')}
                                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                                        />
                                        <Button
                                            label={isProcessing ? "Changing..." : "Change Background"}
                                            icon="pi pi-image"
                                            loading={isProcessing}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleChangeBackground();
                                            }}
                                            disabled={((!generatedImage && !selectedImage) || !imageBackgroundPrompt || isProcessing)}
                                            className="p-button-sm"
                                        />
                                    </div>
                                </div>
                            )}
                            {/* Image Preview - Only show if no image is selected or we're in generate mode */}
                            {(!selectedImage || imageEditMode === 'generate') && (
                                <div className="mt-4 p-3 border rounded bg-gradient-to-r from-green-50 to-teal-50 shadow-sm">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded mr-2">Image</span>
                                        Image Preview:
                                    </h5>
                                    {generatedImage ? (
                                        <>
                                            <div className="border rounded overflow-hidden bg-white shadow-sm">
                                                <img
                                                    src={imageEditedVersion || generatedImage}
                                                    alt="Generated"
                                                    className="max-w-full h-auto mx-auto"
                                                    style={{
                                                        maxHeight: '200px',
                                                        objectFit: 'contain',
                                                        filter: imageStyle !== 'realistic' ? imageStyles[imageStyle].filter : 'none',
                                                        backgroundColor: imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? 'transparent' : undefined
                                                    }}
                                                />
                                            </div>
                                            <div className="mt-3 flex justify-end">
                                                <Button
                                                    label="Add to Canvas"
                                                    icon="pi pi-plus"
                                                    className="p-button-sm p-button-outlined"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleAddImageToCanvas(imageEditedVersion || generatedImage);
                                                    }}
                                                />
                                            </div>
                                        </>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic p-2 bg-white rounded border border-gray-100">
                                            The image will appear here after generation
                                        </p>
                                    )}
                                </div>
                            )}
                            {/* Preview of edits on selected image - Only show if an image is selected and we're not in generate mode */}
                            {selectedImage && imageEditMode !== 'generate' && (
                                <div className="mt-4 p-3 border rounded bg-gradient-to-r from-blue-50 to-indigo-50 shadow-sm">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded mr-2">Edit Preview</span>
                                        Edit Preview:
                                    </h5>
                                    <div className="flex flex-col md:flex-row gap-4">
                                        <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                            <h6 className="text-xs font-medium text-center mb-2 text-gray-500">Original</h6>
                                            <img
                                                src={selectedImage.value}
                                                alt="Original"
                                                className="max-w-full h-auto mx-auto"
                                                style={{
                                                    maxHeight: '150px',
                                                    objectFit: 'contain'
                                                }}
                                            />
                                        </div>
                                        <div className="flex-1 border rounded overflow-hidden bg-white shadow-sm p-2">
                                            <h6 className="text-xs font-medium text-center mb-2 text-gray-500">With Edits</h6>
                                            {imageEditMode === 'background' && imageBackgroundPrompt.includes('transparent') ? (
                                                <div className="relative">
                                                    <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
                                                        {[...Array(64)].map((_, i) => (
                                                            <div
                                                                key={i}
                                                                className={`${(Math.floor(i / 8) + i % 8) % 2 === 0 ? 'bg-gray-200' : 'bg-white'}`}
                                                            ></div>
                                                        ))}
                                                    </div>
                                                    <img
                                                        src={selectedImage.value}
                                                        alt="With Edits"
                                                        className="max-w-full h-auto mx-auto relative"
                                                        style={{
                                                            maxHeight: '150px',
                                                            objectFit: 'contain',
                                                            filter: 'contrast(1.2) brightness(1.1)',
                                                            mixBlendMode: 'multiply'
                                                        }}
                                                    />
                                                </div>
                                            ) : (
                                                <img
                                                    src={selectedImage.value}
                                                    alt="With Edits"
                                                    className="max-w-full h-auto mx-auto"
                                                    style={{
                                                        maxHeight: '150px',
                                                        objectFit: 'contain',
                                                        filter: imageStyle !== 'realistic'
                                                            ? imageStyles[imageStyle].filter
                                                            : imageEditMode === 'enhance'
                                                                ? `contrast(${1 + imageEnhanceLevel/100}) brightness(${1 + imageEnhanceLevel/200}) saturate(${1 + imageEnhanceLevel/100})`
                                                                : 'none'
                                                    }}
                                                />
                                            )}
                                        </div>
                                    </div>
                                    <div className="mt-3 text-xs text-center text-gray-500">
                                        Changes are applied directly to the selected image on the canvas
                                    </div>
                                </div>
                            )}
                        </div>

            </div>
        </div>
    );
};

AIToolsHub.propTypes = {
    onlyImageTab: PropTypes.bool,
    onlyTextTab: PropTypes.bool
};

export default AIToolsHub;
