import { useLocation } from 'react-router-dom';
import { Link } from 'react-router-dom';

import { useGlobalContext } from '@contexts/GlobalContext';
import { useLayout } from '@contexts/LayoutContext';
import { _menuTabs } from '@constants/menuTabs';
import logo from "@images/Logo.svg"

import { Image } from 'primereact/image';

import Banner from './Banner';

function SideMenu() {
  const { userType } = useGlobalContext();
  const {
    isMobile,
    isSidebarCollapsed,
    isBurgerMenuOpen,
    closeMobileMenu
  } = useLayout();

  const location = useLocation();
  const routeName = location.pathname;
  const isGroupMembersPage = routeName === '/members/group';

  const menuTabs = _menuTabs[userType] || _menuTabs.user;

  return (
    <>
      <aside
        className={`bg-[white] p-4 flex flex-col items-center justify-start h-[100vh] transition-all duration-300 ease-in-out
                   ${isMobile
                     ? (isBurgerMenuOpen ? 'w-64' : 'w-0 -translate-x-full')
                     : (isSidebarCollapsed ? 'w-20' : 'w-full')
                   }
                   ${isMobile && isBurgerMenuOpen ? 'fixed z-40 shadow-xl' : ''}
                   ${!isMobile ? 'relative' : ''}`}
      >
        {/* Conditional rendering for mobile to hide content when menu is closed to prevent overflow issues */}
        {(!isMobile || isBurgerMenuOpen) && (
          <>
            {/* Logo - hide when collapsed on desktop */}
            {(!isSidebarCollapsed || isMobile) && (
              <Link to={`/${userType}/dashboard`} className="w-full flex justify-center">
                <img src={logo} alt="Ink Null Logo" style={{ width: '65%' }} />
              </Link>
            )}

            <ul className={`w-full ${(!isSidebarCollapsed || isMobile) ? 'mt-10 m-5' : 'mt-16 m-2'}`}>
              {menuTabs.map((tab, index) => {
                let isActive = false;
                if (isGroupMembersPage) {
                  isActive = tab.activeKey === 'groups';
                } else {
                  isActive = routeName.includes(tab.activeKey);
                }

                return !tab.onlyAdmin || (tab.onlyAdmin && userType === "admin") ? (
                  tab.isWIP ? (
                    <div key={index} className="cursor-not-allowed"> {/* WIP items are not clickable */}
                      <li
                        className={`flex p-1 items-center text-[#939393] opacity-60 ${
                          !isMobile && isSidebarCollapsed ? 'justify-center mb-4 relative group' : ''
                        }`}
                        data-title={`${tab?.title} (Work in Progress)`}
                      >
                        <span className={`${
                          !isMobile && isSidebarCollapsed
                            ? 'mx-auto'
                            : (isMobile && !isBurgerMenuOpen ? 'mr-0' : 'm-3')
                        }`}> {/* Adjust icon margin based on state */}
                          {tab.icon}
                        </span>
                        {((!isMobile && !isSidebarCollapsed) || (isMobile && isBurgerMenuOpen)) && ( // Show title based on state
                           <span className="menu-item-title flex items-center gap-2">
                             {tab?.title}
                             <span className="text-xs bg-[#6B7280] text-white px-2 py-1 rounded-full font-medium">WIP</span>
                           </span>
                        )}

                        {/* Tooltip for collapsed state */}
                        {!isMobile && isSidebarCollapsed && (
                          <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            {tab?.title} (Work in Progress)
                            <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-800"></div>
                          </div>
                        )}
                      </li>
                    </div>
                  ) : (
                    <Link to={tab.route} key={index} onClick={isMobile ? closeMobileMenu : undefined}> {/* Close menu on item click on mobile */}
                      <li
                        className={`flex p-1 items-center ${isActive ? "active_tab" : "text-[#939393]"} ${
                          !isMobile && isSidebarCollapsed ? 'justify-center mb-4 relative group' : ''
                        }`}
                        data-title={tab?.title}
                      >
                        <span className={`${
                          !isMobile && isSidebarCollapsed
                            ? 'mx-auto'
                            : (isMobile && !isBurgerMenuOpen ? 'mr-0' : 'm-3')
                        }`}> {/* Adjust icon margin based on state */}
                          {tab.icon}
                        </span>
                        {((!isMobile && !isSidebarCollapsed) || (isMobile && isBurgerMenuOpen)) && ( // Show title based on state
                           <span className="menu-item-title">{tab?.title}</span>
                        )}

                        {/* Tooltip for collapsed state */}
                        {!isMobile && isSidebarCollapsed && (
                          <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            {tab?.title}
                            <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-800"></div>
                          </div>
                        )}
                      </li>
                    </Link>
                  )
                ) : null;
              })}
            </ul>

            {/* Banner - hide when collapsed on desktop */}
            {(!isSidebarCollapsed || isMobile) && (
              <div className='mt-auto w-full'>
                <Banner />
              </div>
            )}
          </>
        )}
      </aside>
      {/* Optional: Overlay for mobile when burger menu is open */}
      {isMobile && isBurgerMenuOpen && (
        <div
          className="fixed inset-0 bg-black opacity-50 z-30 md:hidden"
          onClick={closeMobileMenu}
        ></div>
      )}
    </>
  );
}

export default SideMenu;