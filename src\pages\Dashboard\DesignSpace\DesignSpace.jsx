import { useState, useEffect, useRef, useCallback } from "react";
import PropTypes from 'prop-types';

import { useDesignSpace } from "@contexts/DesignSpaceContext";
import "./DesignSpaceOverrides.css"; // Import custom CSS to override transparency
import "./RotationHandles.css"; // Import custom CSS for rotation handles
import "./cursors.css"; // Import professional cursors
import "./MobileDesignSpace.css"; // Import mobile-specific styles

import AlignmentContainer from "./components/AlignmentContainer";
import AlignmentControl from "./components/AlignmentControl";
import DuplicateControl from "./components/DuplicateControl";
import DeleteControl from "./components/DeleteControl";
import ResizeInputs from "./components/ResizeInputs";
import TypeControl from "./components/TypeControl";
import Element from "./components/Element";
import CanvaToolbar from "./components/CanvaToolbar";
import DesignSpaceBackground from "./components/DesignSpaceBackground";
import LeftSidebar from "./components/LeftSidebar";
import { FiHelpCircle, FiChevronLeft } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { MdOutlineColorLens } from 'react-icons/md';
import { FaRegStar } from 'react-icons/fa';
import { useGlobalContext } from '@contexts/GlobalContext';
import ColorPicker from "./components/ColorPicker";
import { useUpdateTemplateMutation } from '@quires';
import PortalToolbar from "./components/PortalToolbar";
import axiosInstance from '../../../config/Axios';
import Layout from '../../../pages/Layout';
import WebFont from 'webfontloader';

// دالة لمعالجة background_style
function parseBackgroundStyle(backgroundStyle) {
    if (!backgroundStyle) return '#ffffff';
    
    try {
        // إذا كان JSON string، نحاول تحليله
        if (typeof backgroundStyle === 'string' && backgroundStyle.startsWith('{')) {
            const parsed = JSON.parse(backgroundStyle);
            const bgColor = parsed.backgroundColor || parsed.background || backgroundStyle;
            return bgColor === 'transparent' ? '#ffffff' : bgColor;
        }
        return backgroundStyle === 'transparent' ? '#ffffff' : backgroundStyle;
    } catch (error) {
        console.warn('Error parsing background_style:', error);
        return backgroundStyle === 'transparent' ? '#ffffff' : backgroundStyle;
    }
}

// Helper to measure text size for a given font
function measureText(text, fontSize, fontFamily, fontWeight, fontStyle, lineHeight, maxWidth = null) {
    // Create a hidden span for measurement
    const span = document.createElement('span');
    span.style.position = 'absolute';
    span.style.visibility = 'hidden';
    span.style.whiteSpace = 'pre-wrap';
    span.style.fontSize = fontSize + 'px';
    span.style.fontFamily = fontFamily;
    span.style.fontWeight = fontWeight;
    span.style.fontStyle = fontStyle;
    span.style.lineHeight = lineHeight;
    span.style.padding = '4px';
    span.style.letterSpacing = 'normal';
    span.style.textTransform = 'none';
    span.style.textDecoration = 'none';
    span.style.width = maxWidth ? maxWidth + 'px' : 'auto';
    span.innerText = text || 'Text';
    document.body.appendChild(span);
    const rect = span.getBoundingClientRect();
    document.body.removeChild(span);
    return { width: rect.width, height: rect.height };
}

function fitTextElementWithinBounds(x, y, width, height, cardType) {
  let newWidth = width;
  let newHeight = height;
  let newX = x;
  let newY = y;
  if (cardType) {
    if (newX < 0) {
      newWidth = Math.max(40, newWidth + newX);
      newX = 0;
    }
    if (newY < 0) {
      newHeight = Math.max(24, newHeight + newY);
      newY = 0;
    }
    if (newX + newWidth > cardType.width) {
      newWidth = Math.max(40, cardType.width - newX);
    }
    if (newY + newHeight > cardType.height) {
      newHeight = Math.max(24, cardType.height - newY);
    }
  }
  return { x: newX, y: newY, width: newWidth, height: newHeight };
}

const updateHandlePositions = (elementId) => {
    const domElement = document.querySelector(`[data-element-id="${elementId}"]`);
    if (!domElement) return;

    const handles = domElement.querySelectorAll('.resize-handle');
    handles.forEach(handle => {
        const corner = handle.getAttribute('data-corner');
        if (corner) {
            const position = getResizeHandlePosition(corner);
            Object.assign(handle.style, position);
        }
    });
};

const DesignSpace = ({ updateTemplateData, design, onImageGenerationStart, onSaveAsDesign }) => {
    const [alignmentLines, setAlignmentLines] = useState({ vertical: null, horizontal: null });
    const [toolbarPosition, setToolbarPosition] = useState(null);
    const [toolbarClasses, setToolbarClasses] = useState('');

    // Mobile responsiveness states
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
    const [showMobileHint, setShowMobileHint] = useState(false);
    const hintTimeoutRef = useRef(null);

    const {
        selectedIds, setSelectedIds,
        updateElement, designSpaceRef,
        elements, setElements,
        setSelectedElement, cardType,
        canvasBackgroundStyle,
        bringToFront, sendToBack,
        zoomLevel,
        saveTextStyle,
        zoom,
        groupId
    } = useDesignSpace();


    const clipboardRef = useRef(null);

    const { dialogHandler } = useGlobalContext();

    const [pendingSave, setPendingSave] = useState(false);

    const [draggingElementId, setDraggingElementId] = useState(null);
    // refs for drag threshold
    const dragStartX = useRef(0);
    const dragStartY = useRef(0);
    const isActuallyDragging = useRef(false);

    const [colorPickerTargetId, setColorPickerTargetId] = useState(null);

    // بعد useState الأخرى
    const [activeCrop, setActiveCrop] = useState({ elementId: null, side: null });
    const [activeResize, setActiveResize] = useState({ elementId: null, corner: null });
    const [activeTextFrameResize, setActiveTextFrameResize] = useState({ elementId: null, side: null });
  
    const [shouldOpenCropTab, setShouldOpenCropTab] = useState(false);

    const [initialElements, setInitialElements] = useState([]);
    const [initialBackgroundStyle, setInitialBackgroundStyle] = useState(null);
    const [isDirty, setIsDirty] = useState(false);

    const updateTemplate = useUpdateTemplateMutation();

    const [toolbarRect, setToolbarRect] = useState(null);
    const [toolbarElement, setToolbarElement] = useState(null);

    const [designSpaceRect, setDesignSpaceRect] = useState(null);
    useEffect(() => {
      if (selectedIds.length === 1 && designSpaceRef.current) {
        setDesignSpaceRect(designSpaceRef.current.getBoundingClientRect());
      } else {
        setDesignSpaceRect(null);
      }
    }, [selectedIds, elements, designSpaceRef]);

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            const mobileView = window.innerWidth < 768;
            setIsMobile(mobileView);

            // Add mobile class to body for better mobile styling
            if (mobileView) {
                document.body.classList.add('mobile-design-space');
            } else {
                document.body.classList.remove('mobile-design-space');
            }
        };

        // Initial check
        handleResize();

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
            document.body.classList.remove('mobile-design-space');
            if (hintTimeoutRef.current) {
                clearTimeout(hintTimeoutRef.current);
            }
        };
    }, []);

    // Mobile touch gesture support
    useEffect(() => {
        if (!isMobile) return;

        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;
        let isLongPress = false;
        let longPressTimer = null;

        const handleTouchStart = (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
            isLongPress = false;

            // Set up long press detection
            longPressTimer = setTimeout(() => {
                isLongPress = true;
                // Trigger haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }, 500);
        };

        const handleTouchMove = () => {
            // Cancel long press if user moves finger
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }
        };

        const handleTouchEnd = (e) => {
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }

            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const touchEndTime = Date.now();

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = touchEndTime - touchStartTime;

            // Detect swipe gestures (only if not a long press)
            if (!isLongPress && Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // Swipe right - could be used for navigation
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could be used for navigation
                    console.log('Swipe left detected');
                }
            }


            if (!isLongPress && Math.abs(deltaY) > 50 && deltaTime < 300) {
                if (deltaY > 0) {
                    // Swipe down - could close mobile panels
                    console.log('Swipe down detected');
                } else {
                    // Swipe up - could open mobile panels
                    console.log('Swipe up detected');
                }
            }
        };

        const designSpace = designSpaceRef.current;
        if (designSpace) {
            designSpace.addEventListener('touchstart', handleTouchStart, { passive: true });
            designSpace.addEventListener('touchmove', handleTouchMove, { passive: true });
            designSpace.addEventListener('touchend', handleTouchEnd, { passive: true });

            return () => {
                if (longPressTimer) {
                    clearTimeout(longPressTimer);
                }
                designSpace.removeEventListener('touchstart', handleTouchStart);
                designSpace.removeEventListener('touchmove', handleTouchMove);
                designSpace.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [isMobile, designSpaceRef]);

    // Helper function to get mouse position relative to design space, considering zoom
    const getRelativeMousePosition = (e, designSpaceRect, zoomLevel) => {
        // zoomLevel is a percentage (e.g., 100, 120, 80)
        const scale = zoomLevel / 100;
        const x = (e.clientX - designSpaceRect.left) / scale;
        const y = (e.clientY - designSpaceRect.top) / scale;
        return { x, y };
    };

    const updateToolbarForElement = (element, overrideRect = null) => {
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        let elementRect;
        if (overrideRect) {
            elementRect = {
                left: overrideRect.x + designSpaceRect.left,
                top: overrideRect.y + designSpaceRect.top,
                right: overrideRect.x + designSpaceRect.left + overrideRect.width,
                bottom: overrideRect.y + designSpaceRect.top + overrideRect.height,
                width: overrideRect.width,
                height: overrideRect.height
            };
        } else {
            // Get the DOM element
            const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
            if (!domElement) return;
            elementRect = domElement.getBoundingClientRect();
        }
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        const edgeThreshold = 50;
        
        const toolbarDistance = isMobile ? 80 : 60;
        
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        const isNearBottom = (designSpaceRect.height - (elementRect.bottom - designSpaceRect.top)) < edgeThreshold;
        
        const toolbarWidth = isMobile ? 280 : 220;
        // const toolbarHeight = isMobile ? 50 : 40; // غير مستخدم
        
        const willOverflowRight = (elementRect.right + toolbarWidth > designSpaceRect.right - 8);
        const willOverflowLeft = (elementRect.left - toolbarWidth < designSpaceRect.left + 8);
        
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
        
        const offsetFromEdge = isMobile ? 60 : 40; 
        
        if (isNearTop && (isNearRight || isNearLeft)) {
            let adjustedLeft = elementWidth / 2;
            
            if (isNearRight) {
                adjustedLeft = elementWidth / 2 - offsetFromEdge; 
            }
            if (isNearLeft) {
                adjustedLeft = elementWidth / 2 + offsetFromEdge; 
            }
            
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${adjustedLeft}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if (isNearBottom && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if (isNearBottom && (isNearRight || isNearLeft)) {
            let adjustedLeft = elementWidth / 2;
            
            if (isNearRight) {
                adjustedLeft = elementWidth / 2 - offsetFromEdge; 
            }
            if (isNearLeft) {
                adjustedLeft = elementWidth / 2 + offsetFromEdge; 
            }
            
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${adjustedLeft}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if ((isNearRight && willOverflowRight) || (isNearLeft && willOverflowLeft)) {
            let adjustedLeft = elementWidth / 2;
            
            if (isNearRight) {
                adjustedLeft = elementWidth / 2 - offsetFromEdge;
            }
            if (isNearLeft) {
                adjustedLeft = elementWidth / 2 + offsetFromEdge;
            }
            
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${adjustedLeft}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        } else if (isNearRight) {
            position = {
                ...position,
                left: `-${toolbarDistance + offsetFromEdge}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            position = {
                ...position,
                left: `${elementWidth + 20 + offsetFromEdge}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
        
        setToolbarPosition(position);
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearBottom) classes += ' bottom-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
    };

    const handleMouseDown = (e, id, resizeCorner = null) => {
        if (e.target.closest('.element-controls')) return;
        e.stopPropagation();
        const isTouch = e.touches && e.touches.length > 0;
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        
        // Fix: Get correct mouse position
        const mouseEvent = isTouch ? { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY } : e;
        const { x: startX, y: startY } = getRelativeMousePosition(mouseEvent, designSpaceRect, zoomLevel);
        
        dragStartX.current = mouseEvent.clientX;
        dragStartY.current = mouseEvent.clientY;
        isActuallyDragging.current = false;
        
        let rafId = null;
        let lastTouchX = dragStartX.current;
        let lastTouchY = dragStartY.current;
        let lastDeltaX = 0;
        let lastDeltaY = 0;
        let isResizing = resizeCorner !== null;
        let isDragging = false;
        
        if (!isResizing && !isTouch) {
            isDragging = true;
        }
        
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        
        const element = { ...elements[elementIndex] };
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;
        
        let anchorX = initialX;
        let anchorY = initialY;
        
        if (isResizing) {
            if (resizeCorner === 'top-left') {
                anchorX = initialX + initialWidth;
                anchorY = initialY + initialHeight;
            } else if (resizeCorner === 'top-right') {
                anchorX = initialX;
                anchorY = initialY + initialHeight;
            } else if (resizeCorner === 'bottom-left') {
                anchorX = initialX + initialWidth;
                anchorY = initialY;
            } else if (resizeCorner === 'bottom-right') {
                anchorX = initialX;
                anchorY = initialY;
            }
        }
        
        let paddingWidth = 0;
        let paddingHeight = 0;
        
        if (isResizing && (element.type === 'text' || element.type === 'label') && (resizeCorner === 'top-left' || resizeCorner === 'top-right' || resizeCorner === 'bottom-left' || resizeCorner === 'bottom-right')) {
            const { width: textWidth, height: textHeight } = measureText(
                element.value,
                element.fontSize || 16,
                element.fontFamily || 'Arial, sans-serif',
                element.fontWeight || 'normal',
                element.fontStyle || 'normal',
                element.lineHeight || 1.2
            );
            paddingWidth = (element.width || textWidth) - textWidth;
            paddingHeight = (element.height || textHeight) - textHeight;
        }
        
        const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
            setDraggingElementId(id);
        
        const updateElementPosition = (touchX, touchY) => {
            // Use the provided coordinates or fall back to last known position
            const clientX = typeof touchX === 'number' ? touchX : lastTouchX;
            const clientY = typeof touchY === 'number' ? touchY : lastTouchY;
            
            if (isResizing) {
                if (element.type === 'text' || element.type === 'label') {
                    if (resizeCorner === 'left' || resizeCorner === 'right') {
                        const mouseX = clientX - designSpaceRect.left;
                        let newWidth;
                        if (resizeCorner === 'left') {
                            newWidth = Math.max(40, initialWidth + (initialX - mouseX));
                            let newX = initialX + (initialWidth - newWidth);
                            const LEFT_MARGIN = 20;
                            if (newX < LEFT_MARGIN) {
                                newWidth = initialWidth + (initialX - LEFT_MARGIN);
                                newX = LEFT_MARGIN;
                            }
                            setElements(prev => prev.map((el, idx) => {
                                if (idx !== elementIndex) return el;
                                return { ...el, width: newWidth, x: newX };
                            }));
                            if (domElement) {
                                domElement.style.width = `${newWidth}px`;
                                domElement.style.left = `${newX}px`;
                                domElement.style.transition = 'none';
                            }
                        } else {
                            newWidth = Math.max(40, mouseX - initialX);
                            let newX = initialX;
                            setElements(prev => prev.map((el, idx) => {
                                if (idx !== elementIndex) return el;
                                return { ...el, width: newWidth, x: newX };
                            }));
                            if (domElement) {
                                domElement.style.width = `${newWidth}px`;
                                domElement.style.left = `${newX}px`;
                                domElement.style.transition = 'none';
                            }
                        }
                    } else {
                        // Handle corner resizing for text elements
                        const mouseX = clientX - designSpaceRect.left;
                        const mouseY = clientY - designSpaceRect.top;
                        let dist = Math.sqrt(Math.pow(mouseX - anchorX, 2) + Math.pow(mouseY - anchorY, 2));
                        let initialDist = Math.sqrt(Math.pow(startX - anchorX, 2) + Math.pow(startY - anchorY, 2));
                        let newFontSize = Math.max(8, Math.round((element.fontSize || 16) * (dist / (initialDist || 1))));
                        
                        const { width: newTextWidth, height: newTextHeight } = measureText(
                            element.value,
                            newFontSize,
                            element.fontFamily || 'Arial, sans-serif',
                            element.fontWeight || 'normal',
                            element.fontStyle || 'normal',
                            element.lineHeight || 1.2
                        );
                        
                        const minWidth = 40;
                        const minHeight = 24;
                        const MARGIN = 8;
                        
                        const newWidthRaw = Math.max(newTextWidth + paddingWidth, minWidth);
                        const newHeightRaw = Math.max(newTextHeight + paddingHeight, minHeight);
                        
                        let newX, newY;
                        if (resizeCorner === 'top-left') {
                            newX = anchorX - newWidthRaw;
                            newY = anchorY - newHeightRaw;
                        } else if (resizeCorner === 'top-right') {
                            newX = anchorX;
                            newY = anchorY - newHeightRaw;
                        } else if (resizeCorner === 'bottom-left') {
                            newX = anchorX - newWidthRaw;
                            newY = anchorY;
                        } else if (resizeCorner === 'bottom-right') {
                            newX = anchorX;
                            newY = anchorY;
                        }
                        
                        let newWidth = newWidthRaw;
                        let newHeight = newHeightRaw;
                        
                        const canvasWidth = designSpaceRect.width / scale;
                        const canvasHeight = designSpaceRect.height / scale;
                        
                        let hitBoundary = false;
                        if (newX < MARGIN) {
                            newWidth -= (MARGIN - newX);
                            newX = MARGIN;
                            hitBoundary = true;
                        }
                        if (newY < MARGIN) {
                            newHeight -= (MARGIN - newY);
                            newY = MARGIN;
                            hitBoundary = true;
                        }
                        if (newX + newWidth > canvasWidth - MARGIN) {
                            newWidth = canvasWidth - MARGIN - newX;
                            hitBoundary = true;
                        }
                        if (newY + newHeight > canvasHeight - MARGIN) {
                            newHeight = canvasHeight - MARGIN - newY;
                            hitBoundary = true;
                        }
                        
                        let adjustedFontSize = newFontSize;
                        if (hitBoundary) {
                            let measured = measureText(
                                element.value,
                                adjustedFontSize,
                                element.fontFamily || 'Arial, sans-serif',
                                element.fontWeight || 'normal',
                                element.fontStyle || 'normal',
                                element.lineHeight || 1.2
                            );
                            while ((measured.width + paddingWidth > newWidth || measured.height + paddingHeight > newHeight) && adjustedFontSize > 8) {
                                adjustedFontSize -= 1;
                                measured = measureText(
                                    element.value,
                                    adjustedFontSize,
                                    element.fontFamily || 'Arial, sans-serif',
                                    element.fontWeight || 'normal',
                                    element.fontStyle || 'normal',
                                    element.lineHeight || 1.2
                                );
                            }
                        }
                        
                        setElements(prev => prev.map((el, idx) => {
                            if (idx !== elementIndex) return el;
                            return { ...el, fontSize: adjustedFontSize, width: newWidth, height: newHeight, x: newX, y: newY };
                        }));
                        
                        if (domElement) {
                            domElement.style.fontSize = `${adjustedFontSize}px`;
                            domElement.style.width = `${newWidth}px`;
                            domElement.style.height = `${newHeight}px`;
                            domElement.style.left = `${newX}px`;
                            domElement.style.top = `${newY}px`;
                            domElement.style.transition = 'none';
                        }
                    }
                } else if (element.type === 'img' || element.type === 'qr' || element.type === 'icon' || element.type === 'svg') {
                    // Handle image/QR/icon/svg resizing
                    const mouseX = clientX - designSpaceRect.left;
                    const mouseY = clientY - designSpaceRect.top;
                    let distX = Math.abs(mouseX - anchorX);
                    let distY = Math.abs(mouseY - anchorY);
                    const aspectRatio = initialWidth / initialHeight;
                    let sizeW = distX;
                    let sizeH = distY;
                    
                    if (sizeW / aspectRatio > sizeH) {
                        sizeW = sizeH * aspectRatio;
                    } else {
                        sizeH = sizeW / aspectRatio;
                    }
                    
                    let newWidth = Math.max(20, sizeW);
                    let newHeight = Math.max(20, sizeH);
                    
                    let newX = initialX;
                    let newY = initialY;
                    
                    if (resizeCorner === 'top-left') {
                        newX = anchorX - newWidth;
                        newY = anchorY - newHeight;
                    } else if (resizeCorner === 'top-right') {
                        newX = anchorX;
                        newY = anchorY - newHeight;
                    } else if (resizeCorner === 'bottom-left') {
                        newX = anchorX - newWidth;
                        newY = anchorY;
                    } else if (resizeCorner === 'bottom-right') {
                        newX = anchorX;
                        newY = anchorY;
                    }
                    
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width / scale - newWidth));
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height / scale - newHeight));
                    newWidth = Math.min(newWidth, designSpaceRect.width / scale - newX);
                    newHeight = Math.min(newHeight, designSpaceRect.height / scale - newY);
                    
                    if (domElement) {
                        domElement.style.left = `${newX}px`;
                        domElement.style.top = `${newY}px`;
                        domElement.style.width = `${newWidth}px`;
                        domElement.style.height = `${newHeight}px`;
                        domElement.style.transition = 'none';
                    }
                    
                    setElements(prev => prev.map((el, idx) => {
                        if (idx !== elementIndex) return el;
                        return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
                    }));
                } else {
                    // Handle regular element resizing
                    const mouseX = clientX - designSpaceRect.left;
                    const mouseY = clientY - designSpaceRect.top;
                    let newWidth = initialWidth;
                    let newHeight = initialHeight;
                    let newX = initialX;
                    let newY = initialY;
                    
                    if (resizeCorner === 'top-left') {
                        newX = Math.min(mouseX, anchorX - 20);
                        newY = Math.min(mouseY, anchorY - 20);
                        newWidth = anchorX - newX;
                        newHeight = anchorY - newY;
                    } else if (resizeCorner === 'top-right') {
                        newX = anchorX;
                        newY = Math.min(mouseY, anchorY - 20);
                        newWidth = Math.max(20, mouseX - anchorX);
                        newHeight = anchorY - newY;
                    } else if (resizeCorner === 'bottom-left') {
                        newX = Math.min(mouseX, anchorX - 20);
                        newY = anchorY;
                        newWidth = anchorX - newX;
                        newHeight = Math.max(20, mouseY - anchorY);
                    } else if (resizeCorner === 'bottom-right') {
                        newX = anchorX;
                        newY = anchorY;
                        newWidth = Math.max(20, mouseX - anchorX);
                        newHeight = Math.max(20, mouseY - anchorY);
                    }
                    
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width / scale - newWidth));
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height / scale - newHeight));
                    newWidth = Math.min(newWidth, designSpaceRect.width / scale - newX);
                    newHeight = Math.min(newHeight, designSpaceRect.height / scale - newY);
                    
                    if (domElement) {
                        domElement.style.left = `${newX}px`;
                        domElement.style.top = `${newY}px`;
                        domElement.style.width = `${newWidth}px`;
                        domElement.style.height = `${newHeight}px`;
                        domElement.style.transition = 'none';
                    }
                    
                    setElements(prev => prev.map((el, idx) => {
                        if (idx !== elementIndex) return el;
                        return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
                    }));
                }
            } else {
                // Handle dragging (not resizing)
                if ((element.type === 'img' || element.type === 'qr' || element.type === 'icon') && !isActuallyDragging.current) {
                    return;
                }
                
                const { x: currentX, y: currentY } = getRelativeMousePosition({ clientX: clientX, clientY: clientY }, designSpaceRect, zoomLevel);
                lastDeltaX = currentX - startX;
                lastDeltaY = currentY - startY;
                
                const tempX = Math.max(0, Math.min(initialX + lastDeltaX, designSpaceRect.width / scale - element.width));
                const tempY = Math.max(0, Math.min(initialY + lastDeltaY, designSpaceRect.height / scale - element.height));
                
                if (domElement) {
                    domElement.style.left = `${tempX}px`;
                    domElement.style.top = `${tempY}px`;
                    domElement.style.transition = 'none';
                }
                
                showAlignmentLines(tempX, tempY, element, designSpaceRect);
                
                setElements(prev => prev.map((el, idx) => {
                    if (idx !== elementIndex) return el;
                    return { ...el, x: tempX, y: tempY };
                }));
            }
            
            rafId = null;
        };
        
        const handleMouseMove = (e) => {
            if ((element.type === 'img' || element.type === 'qr' || element.type === 'icon') && !isActuallyDragging.current && !isResizing) {
                const distance = Math.sqrt(Math.pow(e.clientX - dragStartX.current, 2) + Math.pow(e.clientY - dragStartY.current, 2));
                if (distance > 6) {
                    isActuallyDragging.current = true;
                    setDraggingElementId(id);
                    isDragging = true;
                } else {
                    return;
                }
            }
            
            if (!isResizing && !isDragging) return;
            
            lastTouchX = e.clientX;
            lastTouchY = e.clientY;
            
            if (!rafId) {
                rafId = requestAnimationFrame(() => updateElementPosition());
            }
        };
        
        const handleMouseUp = () => {
            isResizing = false; 
            isDragging = false; 
            setAlignmentLines({ vertical: null, horizontal: null });
            window.removeEventListener("mousemove", handleMouseMove);
            window.removeEventListener("mouseup", handleMouseUp);
            setDraggingElementId(null);
            isActuallyDragging.current = false;
            
            setTimeout(() => {
                const dragged = elements[elementIndex];
                if (dragged && selectedIds.includes(dragged.id)) {
                    updateToolbarForElement(dragged);
                }
            }, 0);
            
            setActiveResize({ elementId: null, corner: null });
        };
        
        window.addEventListener("mousemove", handleMouseMove);
        window.addEventListener("mouseup", handleMouseUp);
        
        if (isTouch) {
            const handleTouchMove = (te) => {
                if (!isResizing && !isDragging) return;
                lastTouchX = te.touches[0].clientX;
                lastTouchY = te.touches[0].clientY;
                if (!rafId) rafId = requestAnimationFrame(() => updateElementPosition(lastTouchX, lastTouchY));
            };
            
            const handleTouchEnd = () => {
                isResizing = false; 
                isDragging = false; 
                setAlignmentLines({ vertical: null, horizontal: null });
                window.removeEventListener("touchmove", handleTouchMove);
                window.removeEventListener("touchend", handleTouchEnd);
                setDraggingElementId(null);
                isActuallyDragging.current = false;
                
                setTimeout(() => {
                    const dragged = elements[elementIndex];
                    if (dragged && selectedIds.includes(dragged.id)) {
                        updateToolbarForElement(dragged);
                    }
                }, 0);
                
                setActiveResize({ elementId: null, corner: null });
            };
            
            window.addEventListener("touchmove", handleTouchMove, { passive: false });
            window.addEventListener("touchend", handleTouchEnd, { passive: false });
        }
    };

    // Handle rotation of elements
    const handleRotationStart = (e, id) => {
        e.stopPropagation();
        if (!selectedIds.includes(id)) {
            return;
        }
        const isTouch = e.touches && e.touches.length > 0;
        const element = elements.find(el => el.id === id);
        if (!element) return;
        const domElement = document.querySelector(`[data-element-id="${id}"]`);
        const elementRect = domElement.getBoundingClientRect();
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;

        const centerX = (elementRect.left - designSpaceRect.left) / scale + elementRect.width / (2 * scale);
        const centerY = (elementRect.top - designSpaceRect.top) / scale + elementRect.height / (2 * scale);

        const getPos = evt => {
            if (evt.touches && evt.touches.length > 0) {
                return getRelativeMousePosition({ clientX: evt.touches[0].clientX, clientY: evt.touches[0].clientY }, designSpaceRect, zoomLevel);
            } else {
                return getRelativeMousePosition(evt, designSpaceRect, zoomLevel);
            }
        };
        const { x: mouseX, y: mouseY } = getPos(e);
        const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * (180 / Math.PI);
        const initialRotation = element.rotation || 0;
        let lastClientX = isTouch ? e.touches[0].clientX : 0;
        let lastClientY = isTouch ? e.touches[0].clientY : 0;
        let rotationRafId = null;
        let isRotating = true;
        const updateElementRotation = () => {
            if (!isRotating || !selectedIds.includes(id)) {
                isRotating = false;
                if (rotationRafId) {
                    cancelAnimationFrame(rotationRafId);
                    rotationRafId = null;
                }
                return;
            }
            const { x: moveX, y: moveY } = getRelativeMousePosition({ clientX: lastClientX, clientY: lastClientY }, designSpaceRect, zoomLevel);
            const newAngle = Math.atan2(moveY - centerY, moveX - centerX) * (180 / Math.PI);
            let rotationDelta = newAngle - startAngle;
            const newRotation = initialRotation + rotationDelta;
            if (domElement && selectedIds.includes(id)) {
                domElement.style.transform = `rotate(${newRotation}deg)`;
                domElement.style.transformOrigin = 'center center';
                domElement.style.willChange = 'transform';
                domElement._pendingRotation = newRotation;
            }
            rotationRafId = null;
        };
        const handleRotationMove = (evt) => {
            if (evt.touches && evt.touches.length > 0) {
                lastClientX = evt.touches[0].clientX;
                lastClientY = evt.touches[0].clientY;
            } else {
                lastClientX = evt.clientX;
                lastClientY = evt.clientY;
            }
            if (!rotationRafId) {
                rotationRafId = requestAnimationFrame(updateElementRotation);
            }
        };
        const handleRotationEnd = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }
            window.removeEventListener('mousemove', handleRotationMove);
            window.removeEventListener('mouseup', handleRotationEnd);
            window.removeEventListener('touchmove', handleRotationMove);
            window.removeEventListener('touchend', handleRotationEnd);
            // Apply pending rotation to React state only if element is still selected
            if (selectedIds.includes(id)) {
                const domElementFinal = document.querySelector(`[data-element-id="${id}"]`);
                if (domElementFinal && domElementFinal._pendingRotation !== undefined) {
                    updateElement(id, { rotation: domElementFinal._pendingRotation });
                    delete domElementFinal._pendingRotation;
                }
            }
        };
        if (isTouch) {
            window.addEventListener('touchmove', handleRotationMove, { passive: false });
            window.addEventListener('touchend', handleRotationEnd, { passive: false });
        } else {
            window.addEventListener('mousemove', handleRotationMove);
            window.addEventListener('mouseup', handleRotationEnd);
        }
        // Add cleanup function to remove event listeners when element is deselected
        const cleanup = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;

            }
            window.removeEventListener('mousemove', handleRotationMove);
            window.removeEventListener('mouseup', handleRotationEnd);
            window.removeEventListener('touchmove', handleRotationMove);
            window.removeEventListener('touchend', handleRotationEnd);
        };
        // Store cleanup function on the element
        const domElementForCleanup = document.querySelector(`[data-element-id="${id}"]`);
        if (domElementForCleanup) {
            domElementForCleanup._rotationCleanup = cleanup;
        }
    };


    // Add effect to handle deselection
    useEffect(() => {
        return () => {
            // Cleanup rotation handlers for all elements
            elements.forEach(el => {
                const domElement = document.querySelector(`[data-element-id="${el.id}"]`);
                if (domElement && domElement._rotationCleanup) {
                    domElement._rotationCleanup();
                    delete domElement._rotationCleanup;
                }
            });
        };
    }, [selectedIds]);


    const handleElementClick = (element, e) => {
        e.stopPropagation();
        setTimeout(() => {
            setSelectedElement(element);
            setSelectedIds([element.id]);
            updateToolbarForElement(element);
        }, 0);
    };

    // Function to get the position of resize handles
    const getResizeHandlePosition = (corner) => {
        switch (corner) {
            case "top-left":
                return { top: "-6px", left: "-6px" };
            case "top-right":
                return { top: "-6px", right: "-6px" };
            case "bottom-left":
                return { bottom: "-6px", left: "-6px" };
            case "bottom-right":
                return { bottom: "-6px", right: "-6px" };
            default:
                return {};
        }
    };

    const showAlignmentLines = (x, y, element, designSpaceRect) => {
        let vertical = null;
        let horizontal = null;

        // Tolerance for alignment
        const tolerance = 5;

        // Check alignment with container
        const containerCenterX = designSpaceRect.width / 2;
        const containerCenterY = designSpaceRect.height / 2;

        if (Math.abs(x + element.width / 2 - containerCenterX) < tolerance) {
            vertical = { position: containerCenterX, type: "solid" };
        }

        if (Math.abs(y + element.height / 2 - containerCenterY) < tolerance) {
            horizontal = { position: containerCenterY, type: "solid" };
        }

        // Check alignment with other elements
        elements.forEach((el) => {
            if (el.id !== element.id) {
                // Horizontal alignment checks
                if (Math.abs(el.y - y) < tolerance) {
                    horizontal = { position: el.y, type: "dotted" }; // Top alignment
                } else if (Math.abs(el.y + el.height - y) < tolerance) {
                    horizontal = { position: el.y + el.height, type: "dotted" }; // Bottom aligns with top
                } else if (Math.abs(el.y + el.height / 2 - (y + element.height / 2)) < tolerance) {
                    horizontal = { position: el.y + el.height / 2, type: "dotted" }; // Center horizontally
                }

                // Vertical alignment checks
                if (Math.abs(el.x - x) < tolerance) {
                    vertical = { position: el.x, type: "dotted" }; // Left alignment
                } else if (Math.abs(el.x + el.width - x) < tolerance) {
                    vertical = { position: el.x + el.width, type: "dotted" }; // Right aligns with left
                } else if (Math.abs(el.x + el.width / 2 - (x + element.width / 2)) < tolerance) {
                    vertical = { position: el.x + el.width / 2, type: "dotted" }; // Center vertically
                }
            }
        });

        setAlignmentLines({ vertical, horizontal });
    };


    // Prevent clicks inside the image sidebar or AI tools from deselecting the element
    const handleMainClick = (e) => {
        // Check if the click is inside the image sidebar or AI tools
        const isImageSidebarClick = e.target.closest('.image-edit-sidebar');
        const isAIToolsClick = e.target.closest('.ai-tools-tabs') || e.target.closest('.ai-tools-hub');
        const isTextAssistantClick = e.target.closest('.text-assistant') || e.target.closest('.p-button') || e.target.closest('.p-dropdown') || e.target.closest('.p-slider');
        const isAIToolsButton = e.target.closest('button') && (
            e.target.closest('button').textContent.includes('Apply Style') ||
            e.target.closest('button').textContent.includes('Enhance Image') ||
            e.target.closest('button').textContent.includes('Change Background') ||
            e.target.closest('button').textContent.includes('Apply to Selected') ||
            e.target.closest('button').textContent.includes('Add to Canvas') ||
            e.target.closest('button').textContent.includes('Reset')
        );

        // Don't deselect if clicking in sidebars, AI tools, text assistant, or specific buttons
        if (!isImageSidebarClick && !isAIToolsClick && !isTextAssistantClick && !isAIToolsButton) {
            setSelectedIds([]);
        }
    };

    useEffect(() => {
        const handleKeyDown = (e) => {

            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            if (e.ctrlKey && e.key.toLowerCase() === 'c') {
                if (selectedIds.length === 1) {
                    const el = elements.find(el => el.id === selectedIds[0]);
                    if (el) {
                        clipboardRef.current = JSON.parse(JSON.stringify(el));
                    }
                }
            }

            if (e.ctrlKey && e.key.toLowerCase() === 'v') {
                if (clipboardRef.current) {

                    const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                    const newZIndex = maxZIndex + 1;
                    
                    const newElement = {
                        ...JSON.parse(JSON.stringify(clipboardRef.current)),
                        id: `el_${Date.now()}`,
                        x: (clipboardRef.current.x || 50) + 30,
                        y: (clipboardRef.current.y || 50) + 30,
                        zIndex: newZIndex 
                    };
                    setElements(prev => [...prev, newElement]);
                    setSelectedIds([newElement.id]);
                }
            }


            if (e.key === 'Delete' || e.key === 'Backspace') {
                if (selectedIds.length > 0) {
                    setElements(prev => prev.filter(el => !selectedIds.includes(el.id)));
                    setSelectedIds([]);
                }
            }


            const moveStep = e.shiftKey ? 20 : 5;
            if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && selectedIds.length > 0) {
                e.preventDefault();
                setElements(prev => prev.map(el => {
                    if (!selectedIds.includes(el.id)) return el;
                    let { x, y } = el;
                    if (e.key === 'ArrowUp') y = Math.max(0, y - moveStep);
                    if (e.key === 'ArrowDown') y = y + moveStep;
                    if (e.key === 'ArrowLeft') x = Math.max(0, x - moveStep);
                    if (e.key === 'ArrowRight') x = x + moveStep;
                    return { ...el, x, y };
                }));
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [elements, selectedIds]);


    const handleSaveClick = () => {
        setSelectedIds([]);
        setPendingSave(true);
    };

    // Add state to store group_ids
    const [groupIds, setGroupIds] = useState([]);

    // Add event listener for formGroupIdsChanged
    useEffect(() => {
        const handleFormGroupIdsChanged = (event) => {
            console.log('formGroupIdsChanged event received:', event.detail);
            console.log('group_ids from event:', event.detail.group_ids);
            setGroupIds(event.detail.group_ids || []);
            console.log('Updated groupIds state:', event.detail.group_ids || []);
        };

        window.addEventListener('formGroupIdsChanged', handleFormGroupIdsChanged);

        return () => {
            window.removeEventListener('formGroupIdsChanged', handleFormGroupIdsChanged);
        };
    }, []);

    useEffect(() => {
        if (pendingSave && selectedIds.length === 0) {
            (async () => {
                try {

                    elements.forEach(el => {
                      if (el.type === 'text' || el.type === 'label') {
                        const wrapper = designSpaceRef.current.querySelector(`[data-element-id="${el.id}"]`);
                        if (wrapper) {

                            const textNode = wrapper.querySelector('.user-data') || wrapper.querySelector('div') || wrapper.querySelector('span') || wrapper.querySelector('input');
                          if (textNode) {
                            textNode.style.fontFamily = el.fontFamily || 'Tajawal, Arial, sans-serif';
                            textNode.style.fontSize = (el.fontSize ? el.fontSize + 'px' : '16px');
                            textNode.style.fontWeight = el.fontWeight || 'normal';
                            textNode.style.fontStyle = el.fontStyle || 'normal';
                            textNode.style.lineHeight = el.lineHeight ? String(el.lineHeight) : '1.2';
                            textNode.style.letterSpacing = el.letterSpacing ? String(el.letterSpacing) : 'normal';
                            textNode.style.textDecoration = el.textDecoration || 'none';
                            textNode.style.textTransform = el.textTransform || 'none';
                            textNode.style.color = el.color || '#000000';
                            textNode.style.backgroundColor = el.backgroundColor || 'transparent';
                          }
                        }
                      }
                    });


                    const textElements = designSpaceRef.current.querySelectorAll('.user-data');
                    textElements.forEach(element => {
                        const value = element.textContent.trim();
                        if (value) {
                            element.textContent = `{{${value}}}`;
                        }
                    });
                    const content = designSpaceRef.current.innerHTML;
                    const designSpaceContent = document.getElementById('design-space-content');
                    let actualBackground = '';
                    let actualBackgroundStyle = null;
                    if (designSpaceContent) {
                        const computedStyle = window.getComputedStyle(designSpaceContent);
                        const bgColor = computedStyle.backgroundColor;
                        const bgImage = computedStyle.backgroundImage;
                        if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                            actualBackground = bgColor;
                        } else if (bgImage && bgImage !== 'none') {
                            actualBackground = bgImage;
                            actualBackgroundStyle = {
                                backgroundSize: computedStyle.backgroundSize,
                                backgroundPosition: computedStyle.backgroundPosition,
                                backgroundRepeat: computedStyle.backgroundRepeat
                            };
                        }
                    }

                    const background = actualBackground || canvasBackgroundStyle?.backgroundColor || design?.background || '';
                    const backgroundStyle = actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : (canvasBackgroundStyle ? (typeof canvasBackgroundStyle === 'string' ? canvasBackgroundStyle : JSON.stringify(canvasBackgroundStyle)) : design?.backgroundStyle || '');
                    const templateContent = content;
                    const _formData = new FormData();
                    _formData.append('id', design?.id);
                    _formData.append('name', design?.name || '');
                    _formData.append('template', templateContent);
                    _formData.append('card_type_id', cardType?.id);
                    _formData.append('init_template', JSON.stringify(elements || []));
                    _formData.append('background', background);
                    _formData.append('background_style', backgroundStyle);
                    _formData.append('_method', 'PUT');
                    
                    // Add group_ids if available
                    if (groupIds && groupIds.length > 0) {
                        _formData.append('group_ids', JSON.stringify(groupIds));
                        console.log('Sending group_ids with update:', groupIds);
                        console.log('Sending group_ids JSON:', JSON.stringify(groupIds));
                    } else if (groupId !== null && groupId !== undefined && groupId !== '') {
                        _formData.append('group_id', groupId);
                        console.log('Sending group_id with update:', groupId);
                    } else {
                        _formData.append('group_id', '');
                        console.log('Sending empty group_id for All Groups update');
                    }
                    
                    // Log all form data
                    console.log('Form data being sent:');
                    for (let [key, value] of _formData.entries()) {
                        console.log(`${key}:`, value);
                    }
                    
                    await updateTemplate.mutateAsync({ data: _formData, id: design?.id });
                    textElements.forEach(element => {
                        const value = element.textContent.trim();
                        if (value && value.startsWith('{{') && value.endsWith('}}')) {
                            element.textContent = value.slice(2, -2);
                        }
                    });
                    if (typeof onImageGenerationStart === 'function') {
                        onImageGenerationStart();
                    }
                    setInitialElements(JSON.parse(JSON.stringify(elements)));
                    setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
                } catch (error) {
                    console.error('Error saving design:', error);
                } finally {
            setPendingSave(false);
                }
            })();
        }
    }, [pendingSave, selectedIds]);


    const handleCropHandleMouseDown = (e, id, side) => {
        e.stopPropagation();
        setActiveCrop({ elementId: id, side });
        const isTouch = e.touches && e.touches.length > 0;

        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        if (element.type !== 'img' && element.type !== 'icon') return;


        const startX = isTouch ? e.touches[0].clientX : e.clientX;
        const startY = isTouch ? e.touches[0].clientY : e.clientY;
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;


        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();

        let rafId = null;
        let lastTouchX = startX;
        let lastTouchY = startY;
        let isResizing = true; 

        let objectPosX = 0.5;
        let objectPosY = 0.5;

        const updateCrop = (touchX, touchY) => {
            if (!isResizing) return; 
            console.log('updateCrop', touchX, touchY);
            const clientX = typeof touchX === 'number' ? touchX : lastTouchX;
            const clientY = typeof touchY === 'number' ? touchY : lastTouchY;
            let newX = initialX;
            let newY = initialY;
            let newWidth = initialWidth;
            let newHeight = initialHeight;

            if (element.type === 'img') {
                if (side === 'left') {
                    const delta = clientX - startX;
                    let maxDelta = initialWidth - 40;
                    let minDelta = -initialX; 
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newX = initialX + appliedDelta;
                    newWidth = initialWidth - appliedDelta;
                    objectPosX = 1;
                } else if (side === 'right') {
                    const delta = startX - clientX;
                    let maxDelta = initialWidth - 40;
                    let minDelta = -(designSpaceRect.width - (initialX + initialWidth));
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newWidth = initialWidth - appliedDelta;
                    objectPosX = 0;
                } else if (side === 'top') {
                    const delta = clientY - startY;
                    let maxDelta = initialHeight - 40;
                    let minDelta = -initialY;
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newY = initialY + appliedDelta;
                    newHeight = initialHeight - appliedDelta;
                    objectPosY = 1;
                } else if (side === 'bottom') {
                    const delta = startY - clientY;
                    let maxDelta = initialHeight - 40;
                    let minDelta = -(designSpaceRect.height - (initialY + initialHeight));
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newHeight = initialHeight - appliedDelta;
                    objectPosY = 0;
                }

                const newElements = [...elements];
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY,
                    width: newWidth,
                    height: newHeight,
                    objectPosX: objectPosX,
                    objectPosY: objectPosY
                };
                setElements(newElements);
            } else if (element.type === 'icon') {
                if (side === 'left') {
                    const delta = clientX - startX;
                    let maxDelta = initialWidth - 20; 
                    let minDelta = -initialX; 
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newX = initialX + appliedDelta;
                    newWidth = initialWidth - appliedDelta;
                } else if (side === 'right') {
                    const delta = startX - clientX;
                    let maxDelta = initialWidth - 20;
                    let minDelta = -(designSpaceRect.width - (initialX + initialWidth));
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newWidth = initialWidth - appliedDelta;
                } else if (side === 'top') {
                    const delta = clientY - startY;
                    let maxDelta = initialHeight - 20;
                    let minDelta = -initialY;
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newY = initialY + appliedDelta;
                    newHeight = initialHeight - appliedDelta;
                } else if (side === 'bottom') {
                    const delta = startY - clientY;
                    let maxDelta = initialHeight - 20;
                    let minDelta = -(designSpaceRect.height - (initialY + initialHeight));
                    let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                    newHeight = initialHeight - appliedDelta;
                }

                const newElements = [...elements];
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY,
                    width: newWidth,
                    height: newHeight
                };
                setElements(newElements);
            }
            rafId = null;
        };

        const handleMouseMove = (ev) => {
            if (!isResizing) return;
            lastTouchX = ev.clientX;
            lastTouchY = ev.clientY;
            if (!rafId) rafId = requestAnimationFrame(() => updateCrop());
        };
        const handleMouseUp = () => {
            isResizing = false; 
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            setActiveCrop({ elementId: null, side: null });
        };
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        if (isTouch) {
            const handleTouchMove = (te) => {
                if (!isResizing) return; 
                console.log('touchmove (crop handle)', te.touches[0].clientX, te.touches[0].clientY);
                lastTouchX = te.touches[0].clientX;
                lastTouchY = te.touches[0].clientY;
                if (!rafId) rafId = requestAnimationFrame(() => updateCrop(lastTouchX, lastTouchY));
            };
            const handleTouchEnd = () => {
                isResizing = false; 
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
                setActiveCrop({ elementId: null, side: null });
            };
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd, { passive: false });
        }
    };

    // Handle text frame resize handles
    const handleTextFrameResize = (e, id, side) => {
        e.stopPropagation();
        setActiveTextFrameResize({ elementId: id, side });
        const isTouch = e.touches && e.touches.length > 0;
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        if (element.type !== 'text' && element.type !== 'label') return;
        const startX = isTouch ? e.touches[0].clientX : e.clientX;
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        let rafId = null;
        let lastTouchX = startX;
        let isResizing = true;

        const updateTextFrame = (touchX) => {
            if (!isResizing) return;
            console.log('updateTextFrame', touchX);
            const clientX = typeof touchX === 'number' ? touchX : lastTouchX;
            let newX = initialX;
            let newY = initialY;
            const MARGIN = 8;

            let newWidth = initialWidth;

            if (side === 'left') {
                const mouseX = clientX - designSpaceRect.left;

                let dynamicMinWidth = 40;
                const isDynamic = element.isDynamicField || (typeof element.value === 'string' && element.value.includes('{{'));
                if (isDynamic && typeof element.value === 'string') {

                    const lines = element.value.split('\n');
                    let maxLineWidth = 0;
                    lines.forEach(line => {
                        const { width: lineWidth } = measureText(
                            line,
                            element.fontSize || 16,
                            element.fontFamily || 'Arial, sans-serif',
                            element.fontWeight || 'normal',
                            element.fontStyle || 'normal',
                            element.lineHeight || 1.2
                        );
                        if (lineWidth > maxLineWidth) maxLineWidth = lineWidth;
                    });

                    dynamicMinWidth = Math.max(maxLineWidth + 12, 40);
                }
                let minDelta = -(initialWidth - dynamicMinWidth);

                let maxDelta = initialX - MARGIN;
                let delta = initialX - mouseX;
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newX = initialX - appliedDelta;
                newWidth = initialWidth + appliedDelta;

                if (newX < MARGIN) {

                    newWidth -= (MARGIN - newX);
                    newX = MARGIN;
                }

                const canvasWidth = designSpaceRect.width / scale;
                const RIGHT_MARGIN = 20;
                if (newX + newWidth > canvasWidth - RIGHT_MARGIN) {
                    newWidth = canvasWidth - RIGHT_MARGIN - newX;
                }
            } else if (side === 'right') {
                const mouseX = clientX - designSpaceRect.left;
                let maxDelta = designSpaceRect.width / scale - (initialX + initialWidth); 
                let minDelta = -(initialWidth - 40);
                let delta = mouseX - (initialX + initialWidth);
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newWidth = initialWidth + appliedDelta;
            }
            let minWidth = 40;
            const paddingW = 12;
            if (element.isDynamicField || (typeof element.value === 'string' && element.value.includes('{{'))) {

                let text = element.value || '';

                if (text.startsWith('{{') && text.endsWith('}}')) {
                    text = text.slice(2, -2);
                }
                // split by space or underscore or dash
                const words = text.split(/\s+|_|-/);
                let maxWordWidth = 0;
                for (let word of words) {
                    const { width: w } = measureText(
                        word,
                        element.fontSize || 16,
                        element.fontFamily || 'Arial, sans-serif',
                        element.fontWeight || 'normal',
                        element.fontStyle || 'normal',
                        element.lineHeight || 1.2
                    );
                    if (w > maxWordWidth) maxWordWidth = w;
                }
                minWidth = Math.max(maxWordWidth + paddingW, 40);
            }

            newWidth = Math.max(newWidth, minWidth);

            const paddingH = 8;
            const minHeight = 24;
            const { height: measuredHeight } = measureText(
                element.value,
                element.fontSize || 16,
                element.fontFamily || 'Arial, sans-serif',
                element.fontWeight || 'normal',
                element.fontStyle || 'normal',
                element.lineHeight || 1.2,
                newWidth 
            );
            let newHeight = Math.max(measuredHeight + paddingH, minHeight);


            setElements(prev => prev.map((el, idx) => {
                if (idx !== elementIndex) return el;
                return { ...el, x: newX, y: newY, width: newWidth, height: newHeight };
            }));


            const domElement = document.querySelector(`[data-element-id="${id}"]`);
            if (domElement) {
                domElement.style.left = `${newX}px`;
                domElement.style.top = `${newY}px`;
                domElement.style.width = `${newWidth}px`;
                domElement.style.height = `${newHeight}px`;
                domElement.style.transition = 'none';
            }


            if (domElement) {
                domElement.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.3)';
            }


            updateToolbarForElement(element, { x: newX, y: newY, width: newWidth, height: newHeight });

            rafId = null;
        };

        const handleMouseMove = (ev) => {
            if (!isResizing) return;
            lastTouchX = ev.clientX;
            if (!rafId) rafId = requestAnimationFrame(() => updateTextFrame());
        };

        const handleMouseUp = () => {
            isResizing = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            const domElement = document.querySelector(`[data-element-id="${id}"]`);
            if (domElement) {
                domElement.style.boxShadow = '';
                domElement.style.transition = 'all 0.2s ease';
            }
            setActiveTextFrameResize({ elementId: null, side: null });
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        if (isTouch) {
            const handleTouchMove = (te) => {
                if (!isResizing) return;
                console.log('touchmove (text frame)', te.touches[0].clientX);
                lastTouchX = te.touches[0].clientX;
                if (!rafId) rafId = requestAnimationFrame(() => updateTextFrame(lastTouchX));
            };
            const handleTouchEnd = () => {
                isResizing = false;
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
                const domElement = document.querySelector(`[data-element-id="${id}"]`);
                if (domElement) {
                    domElement.style.boxShadow = '';
                    domElement.style.transition = 'all 0.2s ease';
                }
                setActiveTextFrameResize({ elementId: null, side: null });
            };
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd, { passive: false });
        }
    };

    useEffect(() => {
        if (design?.elements) {
            const loadedElements = JSON.parse(JSON.stringify(design.elements));

            const elementsWithZIndex = loadedElements.map((el, index) => ({
                ...el,
                zIndex: el.zIndex || index + 1
            }));
            setInitialElements(elementsWithZIndex);
            

            setElements(elementsWithZIndex);
        } else if (elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
        }
        if (canvasBackgroundStyle) {
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [design, cardType]);


    useEffect(() => {
        const elementsEqual = JSON.stringify(elements) === JSON.stringify(initialElements);
        const bgEqual = JSON.stringify(canvasBackgroundStyle) === JSON.stringify(initialBackgroundStyle);
        setIsDirty(!(elementsEqual && bgEqual));
    }, [elements, canvasBackgroundStyle, initialElements, initialBackgroundStyle]);


    useEffect(() => {
        if (!isDirty && elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [isDirty]);

    // Auto-fit text box when fontSize or value changes
    useEffect(() => {
        elements.forEach((el, idx) => {
            if ((el.type === 'text' || el.type === 'label') && (el.value || el.fontSize)) {
                // Only auto-fit if not currently resizing horizontally or using text frame handles
                if (!activeResize.elementId || activeResize.elementId !== el.id || (activeResize.corner !== 'left' && activeResize.corner !== 'right')) {
                    // Also check if not using text frame resize handles
                    if (!activeTextFrameResize.elementId || activeTextFrameResize.elementId !== el.id) {
                        const { width, height } = measureText(
                            el.value,
                            el.fontSize || 16,
                            el.fontFamily || 'Arial, sans-serif',
                            el.fontWeight || 'normal',
                            el.fontStyle || 'normal',
                            el.lineHeight || 1.2
                        );
                        // Add some min width/height
                        const minWidth = 40;
                        const minHeight = 24;
                        // Only auto-fit if the element hasn't been manually resized
                        // Check if current width is significantly different from calculated width
                        const widthDifference = Math.abs((el.width || 0) - width);
                        const heightDifference = Math.abs((el.height || 0) - height);
                        if (widthDifference < 5 && heightDifference < 5) {
                            if (((el.width || 0) < width - 2 || (el.height || 0) < height - 2)) {

                                let dims = fitTextElementWithinBounds(el.x, el.y, Math.max(width, minWidth), Math.max(height, minHeight), cardType);
                                setElements(prev => prev.map((item, i) => i === idx ? { ...item, ...dims } : item));
                            }
                        }
                    }
                }
            }
        });
    }, [elements.map(el => el.type === 'text' || el.type === 'label' ? `${el.value}|${el.fontSize}|${el.fontFamily}|${el.fontWeight}|${el.fontStyle}|${el.lineHeight}` : '').join(','), activeResize, activeTextFrameResize]);



    return (
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col'} relative w-full items-start h-full`} onClick={handleMainClick}> {/*should probably refractor it*/}
            {/* Top Toolbar - Dark Background - Hide on mobile */}
            {!isMobile && (
                <div className="w-full bg-gray-800 text-white p-2">
                    {/* Placeholder for top toolbar */}
                </div>
            )}

            {/* Canva Toolbar with Save Button - Hide on mobile */}
            {!isMobile && (
                <CanvaToolbar
                    updateTemplateData={updateTemplateData}
                    saveDesignHandler={handleSaveClick}
                    saveAsHandler={onSaveAsDesign}
                    isDirty={isDirty}
                    isCreateMode={!design}
                    isSaving={pendingSave}
                />
            )}

            {/* Second Toolbar */}
            <div className={`w-full flex items-center p-2 bg-gray-50 border-b border-gray-200 ${isMobile ? 'flex-nowrap gap-2' : ''}`}>
                {/* Left: Back Button */}
                <div className="flex items-center flex-shrink-0">
                    <motion.button
                        className="flex items-center px-3 py-1.5 rounded-md bg-gradient-to-r from-gray-800 to-gray-700 text-white shadow-sm"
                        style={{ minWidth: 70 }}
                        onClick={() => window.history.back()}
                        whileHover={{
                            scale: 1.05,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <motion.span
                            animate={{ x: [0, -3, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                        >
                            <FiChevronLeft size={18} />
                        </motion.span>
                        <span className="ml-1 font-medium">Back</span>
                    </motion.button>
                </div>

                {/* Center: Card Type Dropdown and Save Button (mobile only) */}
                {isMobile && (
                    <div className="flex-1 flex justify-center items-center gap-2">
                        <TypeControl hideLabel={true} />
                        <motion.button
                            className="px-3 py-1.5 rounded-md bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg text-sm font-medium"
                            onClick={onSaveAsDesign}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title="Save as New Design"
                        >
                            Save as New
                        </motion.button>
                    </div>
                )}

                {/* Right: Other Controls (includes TypeControl on web) */}
                <div className="flex items-center flex-shrink-0 ml-auto">
                    {!isMobile && <TypeControl />}
                    {!isMobile && <AlignmentControl />}
                    {!isMobile && <ResizeInputs />}
                    {!isMobile && <DuplicateControl />}
                    {!isMobile && <DeleteControl />}
                    <button
                        className="ml-2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:from-blue-600 hover:to-cyan-600 flex items-center justify-center border-2 border-white/70"
                        style={{ boxShadow: '0 4px 16px 0 rgba(0, 180, 255, 0.15)' }}
                        onClick={() => document.querySelector('.help-guide-button')?.click()}
                        title="Help"
                    >
                        <FiHelpCircle size={isMobile ? 18 : 22} className="drop-shadow" />
                    </button>

                </div>
            </div>

            {/* Main Content Area - Responsive layout */}
            <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} w-full h-full overflow-hidden`}>
                {/* Left Sidebar - Desktop only */}
                {!isMobile && <LeftSidebar isMobile={isMobile} />}

                {/* Canvas Area */}
                <div className={`flex-grow flex flex-col h-full ${isMobile ? 'pb-24' : ''}`}>
                    {cardType && (
                        <div className={`flex-grow flex justify-center items-center ${isMobile ? 'p-2' : 'p-4'} overflow-auto relative design-space-container`}>
                            <DesignSpaceBackground />
                            <div
                                className={`design-space relative shadow-xl ${isMobile ? 'mobile-canvas' : ''} ${zoomLevel !== 100 ? 'no-interactive-zoom' : ''}`}
                                ref={designSpaceRef}
                                style={{
                                    transform: `scale(${zoomLevel / 100})`,
                                    transformOrigin: 'center center',
                                    willChange: 'transform',
                                    transition: 'transform 0.12s cubic-bezier(0.4,0,0.2,1)',
                                    ...(isMobile && {
                                        maxWidth: 'calc(100vw - 16px)',
                                        maxHeight: 'calc(100vh - 200px)'
                                    })
                                }}
                            >
                                <div
                                    id="design-space-content"
                                    onContextMenu={(e) => e.preventDefault()}
                                    style={{
                                        width: `${cardType?.width}px`,
                                        height: `${cardType?.height}px`,
                                        position: "relative",
                                        backgroundColor: design?.background || parseBackgroundStyle(design?.background_style) || '#ffffff',
                                        backgroundImage: 'none',
                                        boxShadow: '0 0 40px rgba(0, 0, 0, 0.25)',
                                        backgroundSize: canvasBackgroundStyle?.backgroundSize,
                                        backgroundBlendMode: 'normal',
                                        backgroundPosition: canvasBackgroundStyle?.backgroundPosition || 'center',
                                        backgroundRepeat: canvasBackgroundStyle?.backgroundRepeat || 'repeat',
                                        opacity: 1
                                    }}
                                >
                                    {/* Professional corner marks */}
                                    <div className="corner-mark top-left"></div>
                                    <div className="corner-mark top-right"></div>
                                    <div className="corner-mark bottom-left"></div>
                                    <div className="corner-mark bottom-right"></div>
                                    {elements?.map((el) => (
                                        <div
                                            key={el.id}
                                            data-element-id={el.id}
                                            style={{
                                                position: "absolute",
                                                top: el.y,
                                                left: el.x,
                                                width: el.width,
                                                height: el.height,
                                                cursor: "move",
                                                zIndex: el.zIndex || 0,
                                                transform: el.rotation ? `rotate(${el.rotation}deg)` : undefined,
                                                transformOrigin: 'center center'
                                            }}
                                            className={`draggable-element ${selectedIds.includes(el.id) ? 'selected' : ''}`}
                                            onMouseDown={(e) => handleMouseDown(e, el.id)}
                                            onClick={(e) => handleElementClick(el, e)}
                                            {...(isMobile ? {
                                                onTouchStart: (e) => handleTouchStartElement(e, el.id),
                                                onTouchMove: handleTouchMoveElement,
                                                onTouchEnd: handleTouchEndElement
                                            } : {})}
                                        >
                                            {/* Apply image-specific styles only to the content, not the toolbar */}
                                            <div 
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    ...(el.style || {})
                                                }}
                                            >
                                                <Element el={el} userData={design?.userData || {}} selectedIds={selectedIds} />
                                            </div>
                                            {/* Crop handles for images only, at mid-sides */}
                                            {selectedIds.includes(el.id) && el.type === 'img' && (
                                                <>
                                                    {/* Top (منتصف الأعلى) */}
                                                    <div
                                                        className="crop-handle crop-handle-top"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-8px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'top')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'top');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Bottom (منتصف الأسفل) */}
                                                    <div
                                                        className="crop-handle crop-handle-bottom"
                                                        style={{
                                                            position: 'absolute',
                                                            bottom: '-8px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'bottom')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'bottom');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Left (منتصف اليسار) */}
                                                    <div
                                                        className="crop-handle crop-handle-left"
                                                        style={{
                                                            position: 'absolute',
                                                            left: '-8px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'left')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'left');
                                                            }
                                                          } : {})}
                                                    />
                                                    {/* Right (منتصف اليمين) */}
                                                    <div
                                                        className="crop-handle crop-handle-right"
                                                        style={{
                                                            position: 'absolute',
                                                            right: '-8px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'right')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleCropHandleMouseDown(e, el.id, 'right');
                                                            }
                                                          } : {})}
                                                    />
                                                </>
                                            )}


                                            {/* Text Frame Resize Handles - للعناصر النصية فقط */}
                                            {selectedIds.includes(el.id) && (el.type === 'text' || el.type === 'label') && (
                                                <>
                                                    {/* Left handle */}
                                                    <div
                                                        className={`text-frame-handle text-frame-handle-left ${activeTextFrameResize.elementId === el.id && activeTextFrameResize.side === 'left' ? 'active' : ''}`}
                                                        style={{
                                                            position: 'absolute',
                                                            left: '-8px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            zIndex: 99999
                                                        }}
                                                        onMouseDown={(e) => handleTextFrameResize(e, el.id, 'left')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleTextFrameResize(e, el.id, 'left');
                                                            }
                                                          } : {})}
                                                        title="Drag to resize text frame width"
                                                    >
                                                        <div className="w-[14px] h-[14px] rounded-full bg-white border border-gray-300"></div>

                                                    </div>
                                                    {/* Right handle */}
                                                    <div
                                                        className={`text-frame-handle text-frame-handle-right ${activeTextFrameResize.elementId === el.id && activeTextFrameResize.side === 'right' ? 'active' : ''}`}
                                                        style={{
                                                            position: 'absolute',
                                                            right: '-8px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            zIndex: 99999
                                                        }}
                                                        onMouseDown={(e) => handleTextFrameResize(e, el.id, 'right')}
                                                        {...(isMobile ? {
                                                            onTouchStart: (e) => {
                                                              e.preventDefault();
                                                              handleTextFrameResize(e, el.id, 'right');
                                                            }
                                                          } : {})}
                                                        title="Drag to resize text frame width"
                                                    >
                                                        <div className="w-[14px] h-[14px] rounded-full bg-white border border-gray-300"></div>
                                                    </div>
                                                </>
                                            )}

                                            {/* زر التدوير الحر */}
                                            {selectedIds.includes(el.id) && (
                                                  <div
                                                    className="rotation-handle"
                                                    onMouseDown={(e) => handleRotationStart(e, el.id)}
                                                    {...(isMobile ? {
                                                      onTouchStart: (e) => { handleRotationStart(e, el.id); }
                                                    } : {})}
                                                  />
                                              )}

                                            {/* Resize/Rotate Handles - منفصلة عن بقية عناصر التحكم */}
                                            {(selectedIds.includes(el.id) || activeResize.elementId === el.id) && (
                                              ['top-left', 'top-right', 'bottom-left', 'bottom-right'].map((corner) => (
                                                    <div
                                                      key={corner}
                                                      className={`resize-handle`}
                                                  data-corner={corner}
                                                      style={{
                                                        cursor: ["top-left", "bottom-right"].includes(corner) ? "nwse-resize" : "nesw-resize",
                                                        ...getResizeHandlePosition(corner),
                                                    pointerEvents: 'all',
                                                    userSelect: 'none',
                                                    touchAction: 'none',
                                                      }}
                                                      onMouseDown={(e) => {
                                                        e.preventDefault();
                                                        setActiveResize({ elementId: el.id, corner });
                                                        handleMouseDown(e, el.id, corner);
                                                      }}
                                                      {...(isMobile ? {
                                                        onTouchStart: (e) => {
                                                          e.preventDefault();
                                                          setActiveResize({ elementId: el.id, corner });
                                                          handleMouseDown(e, el.id, corner);
                                                        },
                                                        onTouchEnd: handleTouchEndElement
                                                      } : {})}
                                                      onContextMenu={(e) => e.preventDefault()}
                                                      title="Drag to resize"
                                                    />
                                              ))
                                              )}
                                        </div>
                                    ))}
                                    <AlignmentContainer alignmentLines={alignmentLines} />
                                    {/* Crop Size Indicator أثناء السحب - داخل الكانفاس */}
                                    {(activeCrop.elementId || activeResize.elementId || activeTextFrameResize.elementId) && (() => {
                                        const el = elements.find(e => e.id === (activeCrop.elementId || activeResize.elementId || activeTextFrameResize.elementId));
                                        if (!el) return null;
                                        const indicatorWidth = 80;
                                        let left = el.x + (el.width / 2) - (indicatorWidth / 2);
                                        let top = el.y + el.height + 22;
                                        if (left + indicatorWidth > cardType?.width) {
                                            left = cardType?.width - indicatorWidth - 8;
                                        }
                                        if (left < 8) {
                                            left = 8;
                                        }
                                        if (top + 28 > cardType?.height) {
                                            top = cardType?.height - 28;
                                        }
                                        return (
                                            <div
                                                style={{
                                                    position: 'absolute',
                                                    left: left,
                                                    top: top,
                                                    background: 'rgba(0,0,0,0.85)',
                                                    color: '#fff',
                                                    borderRadius: 6,
                                                    padding: '2px 10px',
                                                    fontSize: 11,
                                                    fontWeight: 500,
                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.13)',
                                                    zIndex: 2000,
                                                    pointerEvents: 'none',
                                                    userSelect: 'none',
                                                    minWidth: indicatorWidth,
                                                    textAlign: 'center',
                                                }}
                                            >
                                                {((el.type === 'text' || el.type === 'label') && (activeResize.elementId || activeTextFrameResize.elementId)) ? (
                                                    <>
                                                        {(activeResize.corner === 'left' || activeResize.corner === 'right' || activeTextFrameResize.side === 'left' || activeTextFrameResize.side === 'right') ? (
                                                            <>Width: {Math.round(el.width)} px</>
                                                        ) : (
                                                            <>Height: {Math.round(el.height)} px</>
                                                        )}
                                                    </>
                                                ) : (
                                                    <>W: {Math.round(el.width)} px | H: {Math.round(el.height)} px</>
                                                )}
                                            </div>
                                        );
                                    })()}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Mobile Sidebar - Bottom positioned for mobile */}
            {isMobile && <LeftSidebar isMobile={isMobile} />}

            {/* Mobile Zoom Controls */}
            {isMobile && (
                <div className="mobile-zoom-controls">
                    <motion.button
                        className="mobile-zoom-btn"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const newZoomLevel = Math.min(zoomLevel + 10, 200);
                            if (zoomLevel !== newZoomLevel) {
                                zoom(newZoomLevel);
                            }
                        }}
                        disabled={zoomLevel >= 200}
                        title="Zoom In"
                        style={{
                            opacity: zoomLevel >= 200 ? 0.5 : 1,
                            cursor: zoomLevel >= 200 ? 'not-allowed' : 'pointer'
                        }}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="11" y1="8" x2="11" y2="14"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </motion.button>
                    <motion.button
                        className="mobile-zoom-btn"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const newZoomLevel = Math.max(zoomLevel - 10, 50);
                            if (zoomLevel !== newZoomLevel) {
                                zoom(newZoomLevel);
                            }
                        }}
                        disabled={zoomLevel <= 50}
                        title="Zoom Out"
                        style={{
                            opacity: zoomLevel <= 50 ? 0.5 : 1,
                            cursor: zoomLevel <= 50 ? 'not-allowed' : 'pointer'
                        }}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                            <line x1="8" y1="11" x2="14" y2="11"/>
                        </svg>
                    </motion.button>
                    <div className="mobile-zoom-level">
                        {zoomLevel}%
                    </div>
                </div>
            )}

            {/* Mobile Gesture Hint */}
            {isMobile && selectedIds.length > 0 && showMobileHint && (
                <div className="mobile-gesture-hint">
                    <div className="mobile-gesture-hint-content">
                        <div className="mobile-gesture-hint-title">Touch Gestures</div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">👆</span>
                            <span>Tap to select • Drag to move</span>
                        </div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">🤏</span>
                            <span>Pinch to resize element</span>
                        </div>
                        <div className="mobile-gesture-hint-item">
                            <span className="mobile-gesture-icon">📐</span>
                            <span>Drag corners to resize</span>
                        </div>
                    </div>
                </div>
            )}

            {/* ColorPicker العائم بجانب منطقة التصميم مباشرة */}
            {colorPickerTargetId && (
                isMobile ? (
                    <div className="fixed left-0 right-0 bottom-0 z-[99999] bg-gradient-to-t from-gray-900 via-gray-800 to-gray-700 rounded-t-2xl shadow-2xl mobile-content-panel flex flex-col border-t border-gray-700" style={{height: '60vh', maxHeight: '80vh'}}>
                        <div className="w-12 h-1.5 bg-gray-600 rounded-full mx-auto my-3 opacity-60" />
                        <div className="flex-1 overflow-y-auto w-full h-full">
                            <ColorPicker
                                elementId={colorPickerTargetId}
                                open={true}
                                onClose={() => setColorPickerTargetId(null)}
                                isMobile={isMobile}
                                isMobileStyled={true}
                            />
                        </div>
                    </div>
                ) : (
                    <div
                        className="color-picker-container bg-white shadow-2xl rounded-xl"
                        style={{
                            position: 'fixed',
                            top: '22%',
                            left: 'calc(50% + 400px)',
                            transform: 'none',
                            zIndex: 9999,
                            transition: 'all 0.3s ease',
                            pointerEvents: 'auto'
                        }}
                    >
                        <ColorPicker
                            elementId={colorPickerTargetId}
                            open={true}
                            onClose={() => setColorPickerTargetId(null)}
                        />
                    </div>
                )
            )}

            {/* PortalToolbar خارج حلقة العناصر */}
            <PortalToolbar rect={toolbarRect} designSpaceRect={designSpaceRect} isMobile={isMobile}>
              {selectedIds.length === 1 && (() => {
                const el = elements.find(e => e.id === selectedIds[0]);
                if (!el) return null;
                if (
                  draggingElementId !== el.id &&
                  !isActuallyDragging.current &&
                  (!activeResize.elementId && !(activeTextFrameResize.elementId === el.id))
                ) {
                  return (
                    <div className={"portal-toolbar-controls"} style={{zIndex: 99999, position: 'static', display: 'flex', minWidth: 80, minHeight: 24, background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)', color: '#fff', gap: 1, borderRadius: 8, boxShadow: '0 4px 16px rgba(0,0,0,0.25)', padding: '1px 2px', fontSize: 11, height: 28, alignItems: 'center', marginTop: 0}}>
                      {/* زر أيقونة لتغيير اللون بنفس شكل ColorPicker */}
                      <button
                        className="element-control-btn color-picker-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="change color"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const closeEvent = new CustomEvent('closeSidebarTabs');
                          document.dispatchEvent(closeEvent);
                          setTimeout(() => {
                            setColorPickerTargetId(el.id);
                          }, 100);
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const closeEvent = new CustomEvent('closeSidebarTabs');
                            document.dispatchEvent(closeEvent);
                            setTimeout(() => {
                              setColorPickerTargetId(el.id);
                            }, 100);
                          }
                        } : {})}
                      >
                        <MdOutlineColorLens size={15} />
                      </button>
                      <button
                        className="element-control-btn rotate-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="Rotate 90°"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const currentRotation = el.rotation || 0;
                          updateElement(el.id, { rotation: currentRotation + 90 });
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const currentRotation = el.rotation || 0;
                            updateElement(el.id, { rotation: currentRotation + 90 });
                          }
                        } : {})}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                          <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                        </svg>
                      </button>
                      <button
                        className="element-control-btn forward-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="Bring Forward"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          bringToFront(el.id);
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            bringToFront(el.id);
                          }
                        } : {})}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 6.5a.5.5 0 0 1 .5.5v1.5H10a.5.5 0 0 1 0 1H8.5V11a.5.5 0 0 1-1 0V9.5H6a.5.5 0 0 1 0-1h1.5V7a.5.5 0 0 1 .5-.5z"/>
                          <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-7 7 7 7 0 0 0 7 7 7 7 0 0 0 7-7 7 7 0 0 0-7-7z"/>
                        </svg>
                      </button>
                      <button
                        className="element-control-btn backward-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="Send Backward"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          sendToBack(el.id);
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            sendToBack(el.id);
                          }
                        } : {})}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                          <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                        </svg>
                      </button>
                      <button
                        className="element-control-btn duplicate-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="Duplicate"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                          const newZIndex = maxZIndex + 1;
                          const newElement = {
                            ...JSON.parse(JSON.stringify(el)),
                            id: `${el.id}-copy-${Date.now()}`,
                            x: el.x + 20,
                            y: el.y + 20,
                            zIndex: newZIndex
                          };
                          setElements(prev => [...prev, newElement]);
                          setSelectedIds([newElement.id]);
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const maxZIndex = elements.length > 0 ? Math.max(...elements.map(el => el.zIndex || 0)) : 0;
                            const newZIndex = maxZIndex + 1;
                            const newElement = {
                              ...JSON.parse(JSON.stringify(el)),
                              id: `${el.id}-copy-${Date.now()}`,
                              x: el.x + 20,
                              y: el.y + 20,
                              zIndex: newZIndex
                            };
                            setElements(prev => [...prev, newElement]);
                            setSelectedIds([newElement.id]);
                          }
                        } : {})}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                          <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                        </svg>
                      </button>
                      <button
                        className="element-control-btn delete-btn"
                        style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                        title="Delete"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const updatedElements = elements.filter(elem => elem.id !== el.id);
                          setElements(updatedElements);
                          setSelectedIds([]);
                          const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                          if (elementToDelete) {
                            elementToDelete.style.transition = 'all 0.2s ease';
                            elementToDelete.style.transform = 'scale(0.8)';
                            elementToDelete.style.opacity = '0';
                          }
                        }}
                        {...(isMobile ? {
                          onTouchStart: (e) => e.stopPropagation(),
                          onTouchEnd: (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const updatedElements = elements.filter(elem => elem.id !== el.id);
                            setElements(updatedElements);
                            setSelectedIds([]);
                            const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                            if (elementToDelete) {
                              elementToDelete.style.transition = 'all 0.2s ease';
                              elementToDelete.style.transform = 'scale(0.8)';
                              elementToDelete.style.opacity = '0';
                            }
                          }
                        } : {})}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                          <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                        </svg>
                      </button>
                      {(el.type === 'text' || el.type === 'label') && (
                        <button
                          className="element-control-btn save-style-btn"
                          style={{width: 22, height: 22, minWidth: 0, minHeight: 0, fontSize: 11, padding: 0, borderRadius: 6, display: 'flex', alignItems: 'center', justifyContent: 'center'}}
                          title="Save as Text Style"
                          onClick={(e) => {
                            e.stopPropagation();
                            const styleObj = {
                              fontFamily: el.fontFamily,
                              fontSize: el.fontSize,
                              fontWeight: el.fontWeight,
                              color: el.color,
                              backgroundColor: el.backgroundColor,
                              textAlign: el.textAlign,
                              lineHeight: el.lineHeight,
                              letterSpacing: el.letterSpacing,
                              textDecoration: el.textDecoration,
                              textTransform: el.textTransform,
                              fontStyle: el.fontStyle,
                              opacity: el.opacity,
                              textShadow: el.textShadow,
                              WebkitTextStroke: el.WebkitTextStroke,
                              transform: el.transform,
                              textEffect: el.textEffect,
                              textShadowColor: el.textShadowColor,
                              textShadowBlur: el.textShadowBlur,
                              textShadowOffset: el.textShadowOffset
                            };
                            saveTextStyle(styleObj);
                          }}
                        >
                          <FaRegStar size={15} />
                        </button>
                      )}
                    </div>
                  );
                }
                return null;
              })()}
            </PortalToolbar>

            {/* ColorPicker العائم بجانب منطقة التصميم مباشرة */}
            {colorPickerTargetId && (
                <div 
                    className="color-picker-container"
                    style={{
                        position: 'fixed',
                        top: '50%',
                        left: 'calc(50% + 400px)', // بجانب منطقة التصميم مباشرة
                        transform: 'translateY(-50%)',
                        zIndex: 9999,
                        transition: 'all 0.3s ease',
                        pointerEvents: 'auto'
                    }}
                >
                    <ColorPicker
                        elementId={colorPickerTargetId}
                        open={true}
                        onClose={() => setColorPickerTargetId(null)}
                    />
                </div>
            )}


        </div>
    );
};

DesignSpace.propTypes = {
    updateTemplateData: PropTypes.func.isRequired,
    design: PropTypes.object.isRequired,
    onImageGenerationStart: PropTypes.func,
    onSaveAsDesign: PropTypes.func,
};


export default DesignSpace;

